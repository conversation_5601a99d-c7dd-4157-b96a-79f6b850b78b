# STEPPR

A mobile-first hybrid event ticketing app that allows guests to explore and buy digital or physical tickets to parties, concerts, and night events.

## Features

- Browse events by date, location, and genre
- Purchase digital tickets with QR codes
- Support for physical QR tickets for users without smartphones
- QR code check-ins at events
- Seat mapping by ticket type (VIP, Regular, Table)
- Dark UI with custom navigation icons
- Responsive UI

## User Roles

### Guest
- Explore: Browse events by category, date, and location
- STEPPS: View purchased tickets with QR codes
- Profile: Manage account settings

### Host
- Explore: Browse all events
- Tickets: View and manage tickets
- Scan: Scan QR codes for check-ins
- My Events: Create and manage events
- Profile: Manage host account settings

## Tech Stack

- Frontend: React Native (Expo)
- Backend: Supabase
- Authentication: Supabase Auth
- Styling: React Native styling
- QR Code: react-native-qrcode-svg
- Icons: Custom SVG icons with react-native-svg

## Project Structure

```
steppr/
├── assets/              # Images, fonts, etc.
│   └── icons/           # SVG icons for navigation
├── components/          # Reusable UI components
├── constants/           # App constants and theme
├── contexts/            # React contexts
├── hooks/               # Custom hooks
├── navigation/          # Navigation configuration
├── screens/             # App screens
│   ├── auth/            # Authentication screens
│   ├── guest/           # Guest screens
│   └── host/            # Host screens
├── services/            # API and service functions
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
├── App.tsx              # App entry point
├── supabase.ts          # Supabase client configuration
├── metro.config.js      # Metro bundler configuration for SVG
└── declarations.d.ts    # TypeScript declarations for SVG imports
```

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Start the development server: `npm start`
4. Follow the Expo instructions to run on a device or emulator

## License

MIT
