export type Theme = {
  isDark: boolean;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    card: string;
    text: string;
    border: string;
    notification: string;
    error: string;
    success: string;
    warning: string;
    info: string;
  };
  spacing: {
    xs: number;
    s: number;
    m: number;
    l: number;
    xl: number;
    xxl: number;
  };
  borderRadius: {
    small: number;
    medium: number;
    large: number;
    pill: number;
  };
  typography: {
    fontSizes: {
      xs: number;
      s: number;
      m: number;
      l: number;
      xl: number;
      xxl: number;
    };
    fontWeights: {
      regular: string;
      medium: string;
      bold: string;
    };
  };
};

export const lightTheme: Theme = {
  isDark: false,
  colors: {
    primary: '#6200EE',
    secondary: '#03DAC6',
    background: '#FFFFFF',
    card: '#F5F5F5',
    text: '#121212',
    border: '#E0E0E0',
    notification: '#FF3B30',
    error: '#B00020',
    success: '#28A745',
    warning: '#FFC107',
    info: '#17A2B8',
  },
  spacing: {
    xs: 4,
    s: 8,
    m: 16,
    l: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    small: 4,
    medium: 8,
    large: 16,
    pill: 999,
  },
  typography: {
    fontSizes: {
      xs: 12,
      s: 14,
      m: 16,
      l: 18,
      xl: 20,
      xxl: 24,
    },
    fontWeights: {
      regular: '400',
      medium: '500',
      bold: '700',
    },
  },
};

export const darkTheme: Theme = {
  isDark: true,
  colors: {
    primary: '#FFFFFF',
    secondary: '#03DAC6',
    background: '#1A1625',
    card: '#2A2535',
    text: '#FFFFFF',
    border: '#3A3545',
    notification: '#FF453A',
    error: '#CF6679',
    success: '#28A745',
    warning: '#FFC107',
    info: '#17A2B8',
  },
  spacing: {
    xs: 4,
    s: 8,
    m: 16,
    l: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    small: 4,
    medium: 8,
    large: 16,
    pill: 999,
  },
  typography: {
    fontSizes: {
      xs: 12,
      s: 14,
      m: 16,
      l: 18,
      xl: 20,
      xxl: 24,
    },
    fontWeights: {
      regular: '400',
      medium: '500',
      bold: '700',
    },
  },
};
