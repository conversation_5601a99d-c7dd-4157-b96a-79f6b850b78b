import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '../contexts/ThemeContext';
import TabIcon from '../components/TabIcon';

// Guest Screens
import ExploreScreen from '../screens/guest/ExploreScreen';
import EventDetailScreen from '../screens/guest/EventDetailScreen';
import TicketPurchaseScreen from '../screens/guest/TicketPurchaseScreen';
import TicketsScreen from '../screens/guest/TicketsScreen';
import TicketDetailScreen from '../screens/guest/TicketDetailScreen';
import ScanScreen from '../screens/guest/ScanScreen';
import ProfileScreen from '../screens/guest/ProfileScreen';
import EditProfileScreen from '../screens/guest/EditProfileScreen';

// Stack param lists
export type ExploreStackParamList = {
  Explore: undefined;
  EventDetail: { eventId: string };
  TicketPurchase: { eventId: string };
};

export type TicketsStackParamList = {
  Tickets: undefined;
  TicketDetail: { ticketId: string };
};

export type ScanStackParamList = {
  Scan: undefined;
};

export type ProfileStackParamList = {
  Profile: undefined;
  EditProfile: undefined;
};

// Tab param list
export type GuestTabParamList = {
  ExploreStack: undefined;
  TicketsStack: undefined;
  ProfileStack: undefined;
};

// Create the stacks
const ExploreStack = createNativeStackNavigator<ExploreStackParamList>();
const TicketsStack = createNativeStackNavigator<TicketsStackParamList>();
const ScanStack = createNativeStackNavigator<ScanStackParamList>();
const ProfileStack = createNativeStackNavigator<ProfileStackParamList>();
const Tab = createBottomTabNavigator<GuestTabParamList>();

// Stack navigators
function ExploreStackNavigator() {
  return (
    <ExploreStack.Navigator>
      <ExploreStack.Screen name="Explore" component={ExploreScreen} options={{ headerShown: false }} />
      <ExploreStack.Screen name="EventDetail" component={EventDetailScreen} options={{ title: 'Event Details' }} />
      <ExploreStack.Screen name="TicketPurchase" component={TicketPurchaseScreen} options={{ title: 'Buy Ticket' }} />
    </ExploreStack.Navigator>
  );
}

function TicketsStackNavigator() {
  return (
    <TicketsStack.Navigator>
      <TicketsStack.Screen name="Tickets" component={TicketsScreen} options={{ headerShown: false }} />
      <TicketsStack.Screen name="TicketDetail" component={TicketDetailScreen} options={{ title: 'Ticket' }} />
    </TicketsStack.Navigator>
  );
}

function ScanStackNavigator() {
  return (
    <ScanStack.Navigator>
      <ScanStack.Screen name="Scan" component={ScanScreen} options={{ headerShown: false }} />
    </ScanStack.Navigator>
  );
}

function ProfileStackNavigator() {
  return (
    <ProfileStack.Navigator>
      <ProfileStack.Screen name="Profile" component={ProfileScreen} options={{ headerShown: false }} />
      <ProfileStack.Screen name="EditProfile" component={EditProfileScreen} options={{ title: 'Edit Profile' }} />
    </ProfileStack.Navigator>
  );
}

// Main tab navigator
export default function GuestNavigator() {
  const { theme } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.text + '80',
        tabBarStyle: {
          backgroundColor: theme.colors.background,
          borderTopColor: 'transparent',
          height: 80,
          paddingTop: 10,
          paddingBottom: 20,
        },
        headerShown: false,
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      }}
    >
      <Tab.Screen
        name="ExploreStack"
        component={ExploreStackNavigator}
        options={{
          tabBarLabel: 'Explore',
          tabBarIcon: ({ color, size }) => (
            <TabIcon name="explore" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="TicketsStack"
        component={TicketsStackNavigator}
        options={{
          tabBarLabel: 'STEPPS',
          tabBarIcon: ({ color, size }) => (
            <TabIcon name="tickets" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="ProfileStack"
        component={ProfileStackNavigator}
        options={{
          tabBarLabel: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <TabIcon name="profile" color={color} size={size} />
          ),
        }}
      />
    </Tab.Navigator>
  );
}
