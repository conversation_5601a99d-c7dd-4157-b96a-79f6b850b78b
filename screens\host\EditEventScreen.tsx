import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { EventsStackParamList } from '../../navigation/HostNavigator';
import { useTheme } from '../../contexts/ThemeContext';

type Props = NativeStackScreenProps<EventsStackParamList, 'EditEvent'>;

export default function EditEventScreen({ route }: Props) {
  const { eventId } = route.params;
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.text, { color: theme.colors.text }]}>
        Edit Event Screen for event ID: {eventId}
      </Text>
      <Text style={[styles.text, { color: theme.colors.text }]}>
        This screen will allow hosts to edit event details.
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
});
