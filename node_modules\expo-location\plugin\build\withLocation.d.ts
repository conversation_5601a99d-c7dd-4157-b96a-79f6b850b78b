import { ConfigPlugin } from 'expo/config-plugins';
declare const _default: ConfigPlugin<void | {
    locationAlwaysAndWhenInUsePermission?: string | undefined;
    locationAlwaysPermission?: string | undefined;
    locationWhenInUsePermission?: string | undefined;
    isIosBackgroundLocationEnabled?: boolean | undefined;
    isAndroidBackgroundLocationEnabled?: boolean | undefined;
    isAndroidForegroundServiceEnabled?: boolean | undefined;
}>;
export default _default;
