import React, { useState } from 'react';
import { View, Text, Modal, TextInput, Button as R<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, StyleSheet } from 'react-native';
import Button from '../../components/Button';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { TicketsStackParamList } from '../../navigation/GuestNavigator';
import { useTheme } from '../../contexts/ThemeContext';

type Props = NativeStackScreenProps<TicketsStackParamList, 'TicketDetail'>;

// Dummy transfer service (replace with real backend call)
async function transferTicket(ticketId: string, recipient: string) {
  // TODO: Replace with actual backend logic
  return new Promise((resolve) => setTimeout(resolve, 1000));
}

export default function TicketDetailScreen({ route, navigation }: Props) {
  const { ticketId } = route.params;
  const { theme } = useTheme();

  const [modalVisible, setModalVisible] = useState(false);
  const [recipient, setRecipient] = useState('');
  const [loading, setLoading] = useState(false);
  const [sellModalVisible, setSellModalVisible] = useState(false);
  const [resalePrice, setResalePrice] = useState('');
  const [selling, setSelling] = useState(false);

  // Dummy sell service (replace with backend)
  async function sellTicket(ticketId: string, price: string) {
    // TODO: Replace with actual backend logic
    return new Promise((resolve) => setTimeout(resolve, 1000));
  }

  const handleTransfer = async () => {
    setLoading(true);
    try {
      await transferTicket(ticketId, recipient);
      Alert.alert('Success', 'Ticket transferred!');
      setModalVisible(false);
      setRecipient('');
    } catch (e) {
      Alert.alert('Error', 'Could not transfer ticket.');
    }
    setLoading(false);
  };

  const handleSell = async () => {
    setSelling(true);
    try {
      await sellTicket(ticketId, resalePrice);
      Alert.alert('Success', 'Ticket listed for resale!');
      setSellModalVisible(false);
      setResalePrice('');
    } catch (e) {
      Alert.alert('Error', 'Could not list ticket for resale.');
    }
    setSelling(false);
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.text, { color: theme.colors.text }]}>
          Ticket Detail Screen for ticket ID: {ticketId}
        </Text>
        <Text style={[styles.text, { color: theme.colors.text }]}>
          This screen will show the full ticket details and QR code.
        </Text>
      </View>
      <Button title="Transfer Ticket" onPress={() => setModalVisible(true)} />
      <Button title="Sell Ticket" onPress={() => setSellModalVisible(true)} />

      {/* Transfer Ticket Modal */}
      <Modal visible={modalVisible} transparent animationType="slide">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={{ fontWeight: 'bold', marginBottom: 10 }}>Transfer Ticket</Text>
            <TextInput
              placeholder="Recipient Email or Username"
              value={recipient}
              onChangeText={setRecipient}
              style={styles.input}
              autoCapitalize="none"
            />
            <RNButton title={loading ? "Transferring..." : "Confirm Transfer"} onPress={handleTransfer} disabled={loading || !recipient} />
            <RNButton title="Cancel" onPress={() => setModalVisible(false)} color="grey" />
          </View>
        </View>
      </Modal>

      {/* Sell Ticket Modal */}
      <Modal visible={sellModalVisible} transparent animationType="slide">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={{ fontWeight: 'bold', marginBottom: 10 }}>Sell Ticket</Text>
            <TextInput
              placeholder="Resale Price"
              value={resalePrice}
              onChangeText={setResalePrice}
              style={styles.input}
              keyboardType="numeric"
            />
            <RNButton title={selling ? "Listing..." : "Confirm Sale"} onPress={handleSell} disabled={selling || !resalePrice} />
            <RNButton title="Cancel" onPress={() => setSellModalVisible(false)} color="grey" />
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  modalContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0008' },
  modalContent: { backgroundColor: '#fff', padding: 24, borderRadius: 12, width: 300 },
  input: { borderWidth: 1, borderColor: '#ccc', borderRadius: 6, padding: 8, marginBottom: 16 },
});
