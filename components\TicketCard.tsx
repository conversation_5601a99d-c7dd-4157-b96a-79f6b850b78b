import { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions, Button, Modal, TextInput, Alert } from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import { useTheme } from '../contexts/ThemeContext';
import { transferTicket } from '../services/TicketService';

interface TicketCardProps {
  id: string;
  eventName: string;
  date: string;
  time: string;
  ticketType: 'Regular' | 'VIP' | 'Table';
  qrValue: string;
  isUsed: boolean;
  onPress: () => void;
}

const { width } = Dimensions.get('window');
const cardWidth = width * 0.9;

export default function TicketCard({
  id,
  eventName,
  date,
  time,
  ticketType,
  qrValue,
  isUsed,
  onPress,
}: TicketCardProps) {
  const { theme } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [recipient, setRecipient] = useState('');

  const getTicketTypeColor = () => {
    switch (ticketType) {
      case 'VIP':
        return '#FFD700'; // Gold
      case 'Table':
        return '#9C27B0'; // Purple
      default:
        return theme.colors.primary;
    }
  };

  const handleTransfer = async () => {
    try {
      await transferTicket(id, recipient);
      Alert.alert('Success', 'Ticket transferred!');
      setModalVisible(false);
    } catch (e) {
      Alert.alert('Error', e instanceof Error ? e.message : 'An error occurred');
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          backgroundColor: theme.colors.card,
          borderColor: theme.colors.border,
          opacity: isUsed ? 0.6 : 1,
        },
      ]}
      onPress={onPress}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[styles.eventName, { color: theme.colors.text }]} numberOfLines={1}>
            {eventName}
          </Text>
          <View
            style={[
              styles.ticketType,
              { backgroundColor: getTicketTypeColor() },
            ]}
          >
            <Text style={styles.ticketTypeText}>{ticketType}</Text>
          </View>
        </View>

        <View style={styles.details}>
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: theme.colors.text + '80' }]}>
              Date
            </Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>
              {date}
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: theme.colors.text + '80' }]}>
              Time
            </Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>
              {time}
            </Text>
          </View>
        </View>

        <View style={styles.qrContainer}>
          <QRCode value={qrValue} size={120} />
          {isUsed && (
            <View style={styles.usedOverlay}>
              <Text style={styles.usedText}>USED</Text>
            </View>
          )}
        </View>

        <Text style={[styles.ticketId, { color: theme.colors.text + '80' }]}>
          Ticket ID: {id}
        </Text>

        <Button title="Transfer Ticket" onPress={() => setModalVisible(true)} />
        <Modal visible={modalVisible} onRequestClose={() => setModalVisible(false)}>
          <TextInput
            placeholder="Recipient Email"
            value={recipient}
            onChangeText={setRecipient}
          />
          <Button title="Confirm Transfer" onPress={handleTransfer} />
          <Button title="Cancel" onPress={() => setModalVisible(false)} />
        </Modal>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    width: cardWidth,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    marginBottom: 16,
  },
  content: {
    padding: 15,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  eventName: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 10,
  },
  ticketType: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 20,
  },
  ticketTypeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  details: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  detailItem: {
    marginRight: 30,
  },
  detailLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  qrContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    position: 'relative',
  },
  usedOverlay: {
    position: 'absolute',
    width: 120,
    height: 120,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  usedText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    transform: [{ rotate: '-30deg' }],
  },
  ticketId: {
    fontSize: 10,
    textAlign: 'center',
  },
});

