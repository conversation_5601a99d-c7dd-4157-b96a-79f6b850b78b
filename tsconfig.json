{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "jsxImportSource": "react", "lib": ["dom", "esnext"], "module": "esnext", "moduleResolution": "node", "noEmit": true, "resolveJsonModule": true, "skipLibCheck": true, "target": "esnext", "isolatedModules": true, "incremental": true, "plugins": [{"name": "typescript-plugin-css-modules"}]}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}