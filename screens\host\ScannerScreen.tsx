import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  Vibration,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Camera } from 'expo-camera';
import { BarCodeScanner } from 'expo-barcode-scanner';
import { useTheme } from '../../contexts/ThemeContext';
import { supabase } from '../../supabase';
import { useNotifications } from '../../contexts/NotificationContext';

export default function ScannerScreen() {
  const { theme } = useTheme();
  const { sendLocalNotification } = useNotifications();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
  const [eventModalVisible, setEventModalVisible] = useState(false);
  const [scanHistory, setScanHistory] = useState<any[]>([]);
  const [flashMode, setFlashMode] = useState(Camera.Constants.FlashMode.off);
  const [cameraType, setCameraType] = useState(Camera.Constants.Type.back);
  const [isProcessing, setIsProcessing] = useState(false);
  const cameraRef = useRef<Camera>(null);

  // Events from Supabase (mock for now)
  const mockEvents = [
    { id: '1', title: 'Summer Beach Party' },
    { id: '2', title: 'Jazz Night Live' },
    { id: '3', title: 'Tech Conference 2023' },
  ];

  // Request camera permissions
  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  // Mock function to handle QR code scanning
  const handleBarCodeScanned = ({ type, data }: { type: string; data: string }) => {
    setScanned(true);

    // In a real app, we would validate the ticket with Supabase here
    // For demo purposes, we'll just show a success message

    // Add to scan history
    const newScan = {
      id: Math.random().toString(),
      ticketId: data,
      timestamp: new Date().toLocaleTimeString(),
      status: 'Valid',
    };

    setScanHistory([newScan, ...scanHistory]);

    Alert.alert(
      'Ticket Scanned',
      `Ticket ID: ${data}\nStatus: Valid`,
      [{ text: 'OK', onPress: () => setScanned(false) }]
    );
  };

  // Mock function to simulate scanning a ticket
  const simulateScan = () => {
    if (!selectedEvent) {
      Alert.alert('Error', 'Please select an event first');
      return;
    }

    const mockTicketId = `T${Math.floor(1000 + Math.random() * 9000)}-USER${Math.floor(100 + Math.random() * 900)}`;
    handleBarCodeScanned({ type: 'QR', data: mockTicketId });
  };

  if (hasPermission === null) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.text, { color: theme.colors.text }]}>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.text, { color: theme.colors.text }]}>No access to camera</Text>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: theme.colors.primary }]}
          onPress={() => {/* Request permission again */}}
        >
          <Text style={styles.buttonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Ticket Scanner</Text>
      </View>

      <View style={styles.eventSelector}>
        <Text style={[styles.eventSelectorLabel, { color: theme.colors.text }]}>
          Selected Event:
        </Text>
        <TouchableOpacity
          style={[
            styles.eventSelectorButton,
            { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
          ]}
          onPress={() => setEventModalVisible(true)}
        >
          <Text style={[styles.eventSelectorText, { color: theme.colors.text }]}>
            {selectedEvent
              ? mockEvents.find(e => e.id === selectedEvent)?.title || 'Select Event'
              : 'Select Event'}
          </Text>
          <Ionicons name="chevron-down" size={20} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <View
        style={[
          styles.scannerContainer,
          { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
        ]}
      >
        {/* In a real app, this would be the BarCodeScanner component */}
        <View style={styles.mockScanner}>
          <Ionicons name="qr-code" size={100} color={theme.colors.primary} />
          <Text style={[styles.scannerText, { color: theme.colors.text }]}>
            {scanned ? 'Processing...' : 'Ready to scan'}
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.scanButton, { backgroundColor: theme.colors.primary }]}
          onPress={simulateScan}
          disabled={scanned || !selectedEvent}
        >
          <Text style={styles.scanButtonText}>
            {scanned ? 'Processing...' : 'Simulate Scan'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.historyContainer}>
        <Text style={[styles.historyTitle, { color: theme.colors.text }]}>
          Recent Scans
        </Text>

        {scanHistory.length > 0 ? (
          scanHistory.map((scan) => (
            <View
              key={scan.id}
              style={[
                styles.historyItem,
                { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
              ]}
            >
              <View>
                <Text style={[styles.historyTicketId, { color: theme.colors.text }]}>
                  {scan.ticketId}
                </Text>
                <Text style={[styles.historyTimestamp, { color: theme.colors.text + '80' }]}>
                  {scan.timestamp}
                </Text>
              </View>
              <View
                style={[
                  styles.historyStatus,
                  {
                    backgroundColor:
                      scan.status === 'Valid'
                        ? theme.colors.success + '20'
                        : theme.colors.error + '20',
                  },
                ]}
              >
                <Text
                  style={[
                    styles.historyStatusText,
                    {
                      color:
                        scan.status === 'Valid'
                          ? theme.colors.success
                          : theme.colors.error,
                    },
                  ]}
                >
                  {scan.status}
                </Text>
              </View>
            </View>
          ))
        ) : (
          <Text style={[styles.emptyHistoryText, { color: theme.colors.text + '80' }]}>
            No scans yet
          </Text>
        )}
      </View>

      {/* Event Selection Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={eventModalVisible}
        onRequestClose={() => setEventModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: theme.colors.background, borderColor: theme.colors.border },
            ]}
          >
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Select Event
            </Text>

            {mockEvents.map((event) => (
              <TouchableOpacity
                key={event.id}
                style={[
                  styles.modalItem,
                  {
                    backgroundColor:
                      selectedEvent === event.id
                        ? theme.colors.primary + '20'
                        : 'transparent',
                  },
                ]}
                onPress={() => {
                  setSelectedEvent(event.id);
                  setEventModalVisible(false);
                }}
              >
                <Text style={[styles.modalItemText, { color: theme.colors.text }]}>
                  {event.title}
                </Text>
                {selectedEvent === event.id && (
                  <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}

            <TouchableOpacity
              style={[styles.modalCloseButton, { backgroundColor: theme.colors.card }]}
              onPress={() => setEventModalVisible(false)}
            >
              <Text style={[styles.modalCloseButtonText, { color: theme.colors.text }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
  },
  button: {
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignSelf: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  eventSelector: {
    marginBottom: 20,
  },
  eventSelectorLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  eventSelectorButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
  },
  eventSelectorText: {
    fontSize: 16,
  },
  scannerContainer: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 20,
  },
  mockScanner: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerText: {
    marginTop: 10,
    fontSize: 16,
  },
  scanButton: {
    padding: 15,
    alignItems: 'center',
  },
  scanButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  historyContainer: {
    flex: 1,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 10,
  },
  historyTicketId: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  historyTimestamp: {
    fontSize: 12,
  },
  historyStatus: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 20,
  },
  historyStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyHistoryText: {
    textAlign: 'center',
    marginTop: 20,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    borderRadius: 12,
    borderWidth: 1,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  modalItemText: {
    fontSize: 16,
  },
  modalCloseButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  modalCloseButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});
