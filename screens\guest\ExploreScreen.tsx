import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { ExploreStackParamList } from '../../navigation/GuestNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import EventCard from '../../components/EventCard';
import { supabase } from '../../supabase';

type Props = NativeStackScreenProps<ExploreStackParamList, 'Explore'>;

// Mock data for now
const mockEvents = [
  {
    id: '1',
    title: 'Summer Beach Party',
    date: 'Jun 15, 2023',
    location: 'Elegushi Beach',
    imageUrl: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3',
    category: 'Club',
  },
  {
    id: '2',
    title: 'Jazz Night Live',
    date: 'Jun 20, 2023',
    location: 'The Shrine',
    imageUrl: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819',
    category: 'Concert',
  },
  {
    id: '3',
    title: 'Tech Conference 2023',
    date: 'Jun 25, 2023',
    location: 'Landmark Centre',
    imageUrl: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    category: 'Conference',
  },
  {
    id: '4',
    title: 'Afrobeats Night',
    date: 'Jul 1, 2023',
    location: 'Hard Rock Cafe',
    imageUrl: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4',
    category: 'Club',
  },
];

const categories = ['All', 'Club', 'Concert', 'Lounge', 'Festival'];

export default function ExploreScreen({ navigation }: Props) {
  const { theme, toggleTheme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [events, setEvents] = useState(mockEvents);

  // In a real app, we would fetch events from Supabase here
  useEffect(() => {
    // Filter events based on selected category
    if (selectedCategory === 'All') {
      setEvents(mockEvents);
    } else {
      setEvents(mockEvents.filter(event => event.category === selectedCategory));
    }
  }, [selectedCategory]);

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text) {
      const filteredEvents = mockEvents.filter(event =>
        event.title.toLowerCase().includes(text.toLowerCase()) ||
        event.location.toLowerCase().includes(text.toLowerCase())
      );
      setEvents(filteredEvents);
    } else {
      setEvents(mockEvents);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Explore</Text>
        <TouchableOpacity onPress={toggleTheme}>
          <Ionicons
            name={theme.isDark ? 'sunny-outline' : 'moon-outline'}
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
      </View>

      <View style={[styles.searchContainer, { backgroundColor: theme.colors.card }]}>
        <Ionicons name="search-outline" size={20} color={theme.colors.text} />
        <TextInput
          style={[styles.searchInput, { color: theme.colors.text }]}
          placeholder="Search events..."
          placeholderTextColor={theme.colors.text + '80'}
          value={searchQuery}
          onChangeText={handleSearch}
        />
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
      >
        {categories.map(category => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryButton,
              {
                backgroundColor:
                  selectedCategory === category
                    ? theme.colors.primary
                    : theme.colors.card,
              },
            ]}
            onPress={() => setSelectedCategory(category)}
          >
            <Text
              style={[
                styles.categoryText,
                {
                  color:
                    selectedCategory === category
                      ? 'white'
                      : theme.colors.text,
                },
              ]}
            >
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <FlatList
        data={events}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <EventCard
            id={item.id}
            title={item.title}
            date={item.date}
            location={item.location}
            imageUrl={item.imageUrl}
            category={item.category}
            onPress={() => navigation.navigate('EventDetail', { eventId: item.id })}
          />
        )}
        contentContainerStyle={styles.eventsList}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    height: 50,
    borderRadius: 25,
    marginBottom: 20,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontSize: 16,
  },
  categoriesContainer: {
    marginBottom: 20,
  },
  categoryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 10,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  eventsList: {
    paddingBottom: 20,
    alignItems: 'center',
  },
});
