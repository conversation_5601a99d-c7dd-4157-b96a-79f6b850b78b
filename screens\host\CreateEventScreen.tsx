import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { EventsStackParamList } from '../../navigation/HostNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../../components/Button';
import { supabase } from '../../supabase';

type Props = NativeStackScreenProps<EventsStackParamList, 'CreateEvent'>;

// Ticket type interface
interface TicketType {
  id: string;
  name: string;
  price: string;
  description: string;
}

export default function CreateEventScreen({ navigation }: Props) {
  const { theme } = useTheme();
  const { user } = useAuth();
  
  // Event details
  const [eventName, setEventName] = useState('');
  const [eventDate, setEventDate] = useState('');
  const [eventTime, setEventTime] = useState('');
  const [eventLocation, setEventLocation] = useState('');
  const [eventDescription, setEventDescription] = useState('');
  const [eventCategory, setEventCategory] = useState('');
  const [eventImage, setEventImage] = useState<string | null>(null);
  
  // Ticket types
  const [ticketTypes, setTicketTypes] = useState<TicketType[]>([
    { id: '1', name: 'Regular', price: '', description: '' },
  ]);
  
  const [loading, setLoading] = useState(false);

  // Add a new ticket type
  const addTicketType = () => {
    setTicketTypes([
      ...ticketTypes,
      {
        id: Date.now().toString(),
        name: '',
        price: '',
        description: '',
      },
    ]);
  };

  // Remove a ticket type
  const removeTicketType = (id: string) => {
    if (ticketTypes.length === 1) {
      Alert.alert('Error', 'You must have at least one ticket type');
      return;
    }
    setTicketTypes(ticketTypes.filter(ticket => ticket.id !== id));
  };

  // Update ticket type
  const updateTicketType = (id: string, field: keyof TicketType, value: string) => {
    setTicketTypes(
      ticketTypes.map(ticket =>
        ticket.id === id ? { ...ticket, [field]: value } : ticket
      )
    );
  };

  // Mock function to pick an image
  const pickImage = () => {
    // In a real app, we would use expo-image-picker here
    // For demo purposes, we'll just set a placeholder image
    setEventImage('https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3');
  };

  // Validate form
  const validateForm = () => {
    if (!eventName) return 'Please enter an event name';
    if (!eventDate) return 'Please enter an event date';
    if (!eventTime) return 'Please enter an event time';
    if (!eventLocation) return 'Please enter an event location';
    if (!eventDescription) return 'Please enter an event description';
    if (!eventCategory) return 'Please select an event category';
    if (!eventImage) return 'Please upload an event image';
    
    for (const ticket of ticketTypes) {
      if (!ticket.name) return 'Please enter a name for all ticket types';
      if (!ticket.price) return 'Please enter a price for all ticket types';
    }
    
    return null;
  };

  // Create event
  const handleCreateEvent = async () => {
    const error = validateForm();
    if (error) {
      Alert.alert('Error', error);
      return;
    }

    try {
      setLoading(true);
      
      // In a real app, we would create the event in Supabase here
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Success
      Alert.alert(
        'Success',
        'Event created successfully',
        [
          {
            text: 'View Events',
            onPress: () => navigation.navigate('Events'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create event. Please try again.');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Event Details
        </Text>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Event Name</Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: theme.colors.card,
                color: theme.colors.text,
                borderColor: theme.colors.border,
              },
            ]}
            placeholder="Enter event name"
            placeholderTextColor={theme.colors.text + '60'}
            value={eventName}
            onChangeText={setEventName}
          />
        </View>
        
        <View style={styles.formRow}>
          <View style={[styles.formGroup, { flex: 1, marginRight: 10 }]}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Date</Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: theme.colors.card,
                  color: theme.colors.text,
                  borderColor: theme.colors.border,
                },
              ]}
              placeholder="MM/DD/YYYY"
              placeholderTextColor={theme.colors.text + '60'}
              value={eventDate}
              onChangeText={setEventDate}
            />
          </View>
          
          <View style={[styles.formGroup, { flex: 1 }]}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Time</Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: theme.colors.card,
                  color: theme.colors.text,
                  borderColor: theme.colors.border,
                },
              ]}
              placeholder="HH:MM AM/PM"
              placeholderTextColor={theme.colors.text + '60'}
              value={eventTime}
              onChangeText={setEventTime}
            />
          </View>
        </View>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Location</Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: theme.colors.card,
                color: theme.colors.text,
                borderColor: theme.colors.border,
              },
            ]}
            placeholder="Enter event location"
            placeholderTextColor={theme.colors.text + '60'}
            value={eventLocation}
            onChangeText={setEventLocation}
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Category</Text>
          <View
            style={[
              styles.input,
              {
                backgroundColor: theme.colors.card,
                borderColor: theme.colors.border,
              },
            ]}
          >
            <TouchableOpacity
              style={[
                styles.categoryButton,
                {
                  backgroundColor:
                    eventCategory === 'Club' ? theme.colors.primary : 'transparent',
                },
              ]}
              onPress={() => setEventCategory('Club')}
            >
              <Text
                style={[
                  styles.categoryButtonText,
                  {
                    color:
                      eventCategory === 'Club' ? 'white' : theme.colors.text,
                  },
                ]}
              >
                Club
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.categoryButton,
                {
                  backgroundColor:
                    eventCategory === 'Concert' ? theme.colors.primary : 'transparent',
                },
              ]}
              onPress={() => setEventCategory('Concert')}
            >
              <Text
                style={[
                  styles.categoryButtonText,
                  {
                    color:
                      eventCategory === 'Concert' ? 'white' : theme.colors.text,
                  },
                ]}
              >
                Concert
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.categoryButton,
                {
                  backgroundColor:
                    eventCategory === 'Lounge' ? theme.colors.primary : 'transparent',
                },
              ]}
              onPress={() => setEventCategory('Lounge')}
            >
              <Text
                style={[
                  styles.categoryButtonText,
                  {
                    color:
                      eventCategory === 'Lounge' ? 'white' : theme.colors.text,
                  },
                ]}
              >
                Lounge
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Description</Text>
          <TextInput
            style={[
              styles.textArea,
              {
                backgroundColor: theme.colors.card,
                color: theme.colors.text,
                borderColor: theme.colors.border,
              },
            ]}
            placeholder="Enter event description"
            placeholderTextColor={theme.colors.text + '60'}
            value={eventDescription}
            onChangeText={setEventDescription}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Event Image</Text>
          <TouchableOpacity
            style={[
              styles.imageUpload,
              {
                backgroundColor: theme.colors.card,
                borderColor: theme.colors.border,
              },
            ]}
            onPress={pickImage}
          >
            {eventImage ? (
              <Image source={{ uri: eventImage }} style={styles.previewImage} />
            ) : (
              <View style={styles.uploadPlaceholder}>
                <Ionicons name="image-outline" size={40} color={theme.colors.text + '60'} />
                <Text style={[styles.uploadText, { color: theme.colors.text + '60' }]}>
                  Tap to upload image
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Ticket Types
          </Text>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
            onPress={addTicketType}
          >
            <Ionicons name="add" size={20} color="white" />
            <Text style={styles.addButtonText}>Add Ticket</Text>
          </TouchableOpacity>
        </View>
        
        {ticketTypes.map((ticket, index) => (
          <View
            key={ticket.id}
            style={[
              styles.ticketTypeContainer,
              {
                backgroundColor: theme.colors.card,
                borderColor: theme.colors.border,
              },
            ]}
          >
            <View style={styles.ticketTypeHeader}>
              <Text style={[styles.ticketTypeTitle, { color: theme.colors.text }]}>
                Ticket Type {index + 1}
              </Text>
              <TouchableOpacity onPress={() => removeTicketType(ticket.id)}>
                <Ionicons name="close-circle" size={24} color={theme.colors.error} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: theme.colors.text }]}>Name</Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: theme.colors.background,
                    color: theme.colors.text,
                    borderColor: theme.colors.border,
                  },
                ]}
                placeholder="e.g. Regular, VIP, Table"
                placeholderTextColor={theme.colors.text + '60'}
                value={ticket.name}
                onChangeText={(value) => updateTicketType(ticket.id, 'name', value)}
              />
            </View>
            
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: theme.colors.text }]}>Price (₦)</Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: theme.colors.background,
                    color: theme.colors.text,
                    borderColor: theme.colors.border,
                  },
                ]}
                placeholder="Enter price"
                placeholderTextColor={theme.colors.text + '60'}
                value={ticket.price}
                onChangeText={(value) => updateTicketType(ticket.id, 'price', value)}
                keyboardType="numeric"
              />
            </View>
            
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: theme.colors.text }]}>Description</Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: theme.colors.background,
                    color: theme.colors.text,
                    borderColor: theme.colors.border,
                  },
                ]}
                placeholder="Enter description"
                placeholderTextColor={theme.colors.text + '60'}
                value={ticket.description}
                onChangeText={(value) => updateTicketType(ticket.id, 'description', value)}
              />
            </View>
          </View>
        ))}
      </View>

      <View style={styles.footer}>
        <Button
          title="Cancel"
          onPress={() => navigation.goBack()}
          variant="outline"
          style={{ flex: 1, marginRight: 10 }}
        />
        <Button
          title={loading ? 'Creating...' : 'Create Event'}
          onPress={handleCreateEvent}
          disabled={loading}
          loading={loading}
          style={{ flex: 1 }}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 20,
  },
  addButtonText: {
    color: 'white',
    marginLeft: 5,
  },
  formGroup: {
    marginBottom: 15,
  },
  formRow: {
    flexDirection: 'row',
  },
  label: {
    fontSize: 14,
    marginBottom: 5,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 15,
  },
  textArea: {
    height: 100,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingTop: 10,
  },
  imageUpload: {
    height: 200,
    borderWidth: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },
  uploadPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadText: {
    marginTop: 10,
  },
  previewImage: {
    width: '100%',
    height: '100%',
  },
  categoryButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  categoryButtonText: {
    fontWeight: '500',
  },
  ticketTypeContainer: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
  },
  ticketTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  ticketTypeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    flexDirection: 'row',
    marginBottom: 30,
  },
});
