import React from 'react';
import { View, StyleSheet } from 'react-native';

// Import SVG components
import ExploreIcon from '../assets/icons/explore-icon.svg';
import TicketsIcon from '../assets/icons/tickets-icon.svg';
import ScanIcon from '../assets/icons/scan-icon.svg';
import EventsIcon from '../assets/icons/events-icon.svg';
import ProfileIcon from '../assets/icons/profile-icon.svg';

type IconType = 'explore' | 'tickets' | 'scan' | 'events' | 'profile';

interface TabIconProps {
  name: IconType;
  color: string;
  size?: number;
}

const TabIcon: React.FC<TabIconProps> = ({ name, color, size = 24 }) => {
  const iconProps = {
    width: size,
    height: size,
    color: color,
  };

  return (
    <View style={styles.container}>
      {name === 'explore' && <ExploreIcon {...iconProps} />}
      {name === 'tickets' && <TicketsIcon {...iconProps} />}
      {name === 'scan' && <ScanIcon {...iconProps} />}
      {name === 'events' && <EventsIcon {...iconProps} />}
      {name === 'profile' && <ProfileIcon {...iconProps} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default TabIcon;
