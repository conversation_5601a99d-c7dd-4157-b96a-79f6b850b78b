import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { EventsStackParamList } from '../../navigation/HostNavigator';
import { useTheme } from '../../contexts/ThemeContext';

type Props = NativeStackScreenProps<EventsStackParamList, 'EventDetails'>;

export default function EventDetailsScreen({ route }: Props) {
  const { eventId } = route.params;
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.text, { color: theme.colors.text }]}>
        Event Details Screen for event ID: {eventId}
      </Text>
      <Text style={[styles.text, { color: theme.colors.text }]}>
        This screen will show detailed information about the event for hosts.
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
});
