// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		1125B2DA1C4AD3DA007D0023 /* AIRMap.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2BE1C4AD3DA007D0023 /* AIRMap.m */; };
		1125B2DB1C4AD3DA007D0023 /* AIRMapCallout.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2C01C4AD3DA007D0023 /* AIRMapCallout.m */; };
		1125B2DC1C4AD3DA007D0023 /* AIRMapCalloutManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2C21C4AD3DA007D0023 /* AIRMapCalloutManager.m */; };
		1125B2DD1C4AD3DA007D0023 /* AIRMapCircle.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2C41C4AD3DA007D0023 /* AIRMapCircle.m */; };
		1125B2DE1C4AD3DA007D0023 /* AIRMapCircleManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2C61C4AD3DA007D0023 /* AIRMapCircleManager.m */; };
		1125B2DF1C4AD3DA007D0023 /* AIRMapCoordinate.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2C81C4AD3DA007D0023 /* AIRMapCoordinate.m */; };
		1125B2E01C4AD3DA007D0023 /* AIRMapManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2CA1C4AD3DA007D0023 /* AIRMapManager.m */; };
		1125B2E11C4AD3DA007D0023 /* AIRMapMarker.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2CC1C4AD3DA007D0023 /* AIRMapMarker.m */; };
		1125B2E21C4AD3DA007D0023 /* AIRMapMarkerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2CE1C4AD3DA007D0023 /* AIRMapMarkerManager.m */; };
		1125B2E31C4AD3DA007D0023 /* AIRMapPolygon.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2D01C4AD3DA007D0023 /* AIRMapPolygon.m */; };
		1125B2E41C4AD3DA007D0023 /* AIRMapPolygonManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2D21C4AD3DA007D0023 /* AIRMapPolygonManager.m */; };
		1125B2E51C4AD3DA007D0023 /* AIRMapPolyline.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2D41C4AD3DA007D0023 /* AIRMapPolyline.m */; };
		1125B2E61C4AD3DA007D0023 /* AIRMapPolylineManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2D61C4AD3DA007D0023 /* AIRMapPolylineManager.m */; };
		1125B2F21C4AD445007D0023 /* SMCalloutView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1125B2F11C4AD445007D0023 /* SMCalloutView.m */; };
		19DABC7F1E7C9D3C00F41150 /* RCTConvert+AirMap.m in Sources */ = {isa = PBXBuildFile; fileRef = 19DABC7E1E7C9D3C00F41150 /* RCTConvert+AirMap.m */; };
		2163AA501FEAEDD100BBEC95 /* AIRMapPolylineRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = 2163AA4F1FEAEDD100BBEC95 /* AIRMapPolylineRenderer.m */; };
		4C99C9DE2226CF2800A8693E /* AIRWeakTimerReference.m in Sources */ = {isa = PBXBuildFile; fileRef = 4C99C9DD2226CF2800A8693E /* AIRWeakTimerReference.m */; };
		4C99C9E12226D8C400A8693E /* AIRWeakMapReference.m in Sources */ = {isa = PBXBuildFile; fileRef = 4C99C9E02226D8C400A8693E /* AIRWeakMapReference.m */; };
		4E0CFBDE2B388F2B0017E126 /* RCTComponentData+Maps.m in Sources */ = {isa = PBXBuildFile; fileRef = 4E0CFBDD2B388F2B0017E126 /* RCTComponentData+Maps.m */; };
		53D31636202E723B00B55447 /* AIRMapOverlayManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 53D31635202E723B00B55447 /* AIRMapOverlayManager.m */; };
		53D3163A202E72FC00B55447 /* AIRMapOverlay.m in Sources */ = {isa = PBXBuildFile; fileRef = 53D31639202E72FC00B55447 /* AIRMapOverlay.m */; };
		53D3163D202E734F00B55447 /* AIRMapOverlayRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = 53D3163C202E734F00B55447 /* AIRMapOverlayRenderer.m */; };
		628F81201FD16DF80058313A /* AIRMapLocalTile.m in Sources */ = {isa = PBXBuildFile; fileRef = 628F811F1FD16DF80058313A /* AIRMapLocalTile.m */; };
		628F81231FD16EFA0058313A /* AIRMapLocalTileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 628F81221FD16EFA0058313A /* AIRMapLocalTileManager.m */; };
		62AEC4D41FD5A0AA003225E0 /* AIRMapLocalTileOverlay.m in Sources */ = {isa = PBXBuildFile; fileRef = 62AEC4D31FD5A0AA003225E0 /* AIRMapLocalTileOverlay.m */; };
		8B19A3C82257BBDF00BB8735 /* AIRMapCalloutSubview.m in Sources */ = {isa = PBXBuildFile; fileRef = 8B19A3C42257BBDE00BB8735 /* AIRMapCalloutSubview.m */; };
		8B19A3C92257BBDF00BB8735 /* AIRMapCalloutSubviewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 8B19A3C72257BBDF00BB8735 /* AIRMapCalloutSubviewManager.m */; };
		8BC85FB02107CFEC0006CEA5 /* AIRGoogleMapOverlay.m in Sources */ = {isa = PBXBuildFile; fileRef = 8BC85FAF2107CFEC0006CEA5 /* AIRGoogleMapOverlay.m */; };
		9B9498CA2017EFB800158761 /* AIRGoogleMapUrlTile.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498A62017EFB400158761 /* AIRGoogleMapUrlTile.m */; };
		9B9498CB2017EFB800158761 /* AIRGoogleMapURLTileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498A72017EFB400158761 /* AIRGoogleMapURLTileManager.m */; };
		9B9498CC2017EFB800158761 /* AIRGMSPolygon.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498A82017EFB400158761 /* AIRGMSPolygon.m */; };
		9B9498CD2017EFB800158761 /* AIRGoogleMapCallout.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498AC2017EFB400158761 /* AIRGoogleMapCallout.m */; };
		9B9498CE2017EFB800158761 /* AIRGMSMarker.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498AE2017EFB500158761 /* AIRGMSMarker.m */; };
		9B9498CF2017EFB800158761 /* AIRGMSPolyline.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498AF2017EFB500158761 /* AIRGMSPolyline.m */; };
		9B9498D02017EFB800158761 /* AIRGoogleMapPolylineManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498B12017EFB500158761 /* AIRGoogleMapPolylineManager.m */; };
		9B9498D12017EFB800158761 /* AIRGoogleMapCircle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498B32017EFB500158761 /* AIRGoogleMapCircle.m */; };
		9B9498D22017EFB800158761 /* AIRGoogleMapMarkerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498B52017EFB500158761 /* AIRGoogleMapMarkerManager.m */; };
		9B9498D32017EFB800158761 /* AIRGoogleMap.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498B72017EFB500158761 /* AIRGoogleMap.m */; };
		9B9498D42017EFB800158761 /* RCTConvert+GMSMapViewType.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498B92017EFB600158761 /* RCTConvert+GMSMapViewType.m */; };
		9B9498D52017EFB800158761 /* AIRGoogleMapPolyline.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498BB2017EFB600158761 /* AIRGoogleMapPolyline.m */; };
		9B9498D62017EFB800158761 /* AIRGoogleMapCircleManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498BC2017EFB600158761 /* AIRGoogleMapCircleManager.m */; };
		9B9498D72017EFB800158761 /* AIRGoogleMapManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498BD2017EFB600158761 /* AIRGoogleMapManager.m */; };
		9B9498D92017EFB800158761 /* AIRGoogleMapCalloutManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498C42017EFB700158761 /* AIRGoogleMapCalloutManager.m */; };
		9B9498DA2017EFB800158761 /* AIRGoogleMapPolygon.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498C62017EFB800158761 /* AIRGoogleMapPolygon.m */; };
		9B9498DB2017EFB800158761 /* AIRGoogleMapMarker.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498C72017EFB800158761 /* AIRGoogleMapMarker.m */; };
		9B9498DC2017EFB800158761 /* AIRGoogleMapPolygonManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B9498C92017EFB800158761 /* AIRGoogleMapPolygonManager.m */; };
		A8494E28218891020092506D /* AIRMapWMSTileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A8494E24218891020092506D /* AIRMapWMSTileManager.m */; };
		A8494E29218891020092506D /* AIRMapWMSTile.m in Sources */ = {isa = PBXBuildFile; fileRef = A8494E27218891020092506D /* AIRMapWMSTile.m */; };
		A8494E2E218891180092506D /* AIRGoogleMapWMSTileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A8494E2A218891180092506D /* AIRGoogleMapWMSTileManager.m */; };
		A8494E2F218891180092506D /* AIRGoogleMapWMSTile.m in Sources */ = {isa = PBXBuildFile; fileRef = A8494E2D218891180092506D /* AIRGoogleMapWMSTile.m */; };
		B5EA3BA92098E22B000E7AFD /* AIRDummyView.m in Sources */ = {isa = PBXBuildFile; fileRef = B5EA3BA72098E22B000E7AFD /* AIRDummyView.m */; };
		BE2E4EC92621F63C00CC7F2E /* AIRMapUrlTileCachedOverlay.m in Sources */ = {isa = PBXBuildFile; fileRef = BE2E4EC82621F63C00CC7F2E /* AIRMapUrlTileCachedOverlay.m */; };
		DA6C26381C9E2AFE0035349F /* AIRMapUrlTile.m in Sources */ = {isa = PBXBuildFile; fileRef = DA6C26371C9E2AFE0035349F /* AIRMapUrlTile.m */; };
		DA6C263E1C9E324A0035349F /* AIRMapUrlTileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DA6C263D1C9E324A0035349F /* AIRMapUrlTileManager.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		11FA5C4F1C4A1296003AC2EE /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1125B2BD1C4AD3DA007D0023 /* AIRMap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMap.h; path = AirMaps/AIRMap.h; sourceTree = SOURCE_ROOT; };
		1125B2BE1C4AD3DA007D0023 /* AIRMap.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMap.m; path = AirMaps/AIRMap.m; sourceTree = SOURCE_ROOT; };
		1125B2BF1C4AD3DA007D0023 /* AIRMapCallout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapCallout.h; path = AirMaps/AIRMapCallout.h; sourceTree = SOURCE_ROOT; };
		1125B2C01C4AD3DA007D0023 /* AIRMapCallout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapCallout.m; path = AirMaps/AIRMapCallout.m; sourceTree = SOURCE_ROOT; };
		1125B2C11C4AD3DA007D0023 /* AIRMapCalloutManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapCalloutManager.h; path = AirMaps/AIRMapCalloutManager.h; sourceTree = SOURCE_ROOT; };
		1125B2C21C4AD3DA007D0023 /* AIRMapCalloutManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapCalloutManager.m; path = AirMaps/AIRMapCalloutManager.m; sourceTree = SOURCE_ROOT; };
		1125B2C31C4AD3DA007D0023 /* AIRMapCircle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapCircle.h; path = AirMaps/AIRMapCircle.h; sourceTree = SOURCE_ROOT; };
		1125B2C41C4AD3DA007D0023 /* AIRMapCircle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapCircle.m; path = AirMaps/AIRMapCircle.m; sourceTree = SOURCE_ROOT; };
		1125B2C51C4AD3DA007D0023 /* AIRMapCircleManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapCircleManager.h; path = AirMaps/AIRMapCircleManager.h; sourceTree = SOURCE_ROOT; };
		1125B2C61C4AD3DA007D0023 /* AIRMapCircleManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapCircleManager.m; path = AirMaps/AIRMapCircleManager.m; sourceTree = SOURCE_ROOT; };
		1125B2C71C4AD3DA007D0023 /* AIRMapCoordinate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapCoordinate.h; path = AirMaps/AIRMapCoordinate.h; sourceTree = SOURCE_ROOT; };
		1125B2C81C4AD3DA007D0023 /* AIRMapCoordinate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapCoordinate.m; path = AirMaps/AIRMapCoordinate.m; sourceTree = SOURCE_ROOT; };
		1125B2C91C4AD3DA007D0023 /* AIRMapManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapManager.h; path = AirMaps/AIRMapManager.h; sourceTree = SOURCE_ROOT; };
		1125B2CA1C4AD3DA007D0023 /* AIRMapManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapManager.m; path = AirMaps/AIRMapManager.m; sourceTree = SOURCE_ROOT; };
		1125B2CB1C4AD3DA007D0023 /* AIRMapMarker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapMarker.h; path = AirMaps/AIRMapMarker.h; sourceTree = SOURCE_ROOT; };
		1125B2CC1C4AD3DA007D0023 /* AIRMapMarker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapMarker.m; path = AirMaps/AIRMapMarker.m; sourceTree = SOURCE_ROOT; };
		1125B2CD1C4AD3DA007D0023 /* AIRMapMarkerManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapMarkerManager.h; path = AirMaps/AIRMapMarkerManager.h; sourceTree = SOURCE_ROOT; };
		1125B2CE1C4AD3DA007D0023 /* AIRMapMarkerManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapMarkerManager.m; path = AirMaps/AIRMapMarkerManager.m; sourceTree = SOURCE_ROOT; };
		1125B2CF1C4AD3DA007D0023 /* AIRMapPolygon.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapPolygon.h; path = AirMaps/AIRMapPolygon.h; sourceTree = SOURCE_ROOT; };
		1125B2D01C4AD3DA007D0023 /* AIRMapPolygon.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapPolygon.m; path = AirMaps/AIRMapPolygon.m; sourceTree = SOURCE_ROOT; };
		1125B2D11C4AD3DA007D0023 /* AIRMapPolygonManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapPolygonManager.h; path = AirMaps/AIRMapPolygonManager.h; sourceTree = SOURCE_ROOT; };
		1125B2D21C4AD3DA007D0023 /* AIRMapPolygonManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapPolygonManager.m; path = AirMaps/AIRMapPolygonManager.m; sourceTree = SOURCE_ROOT; };
		1125B2D31C4AD3DA007D0023 /* AIRMapPolyline.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapPolyline.h; path = AirMaps/AIRMapPolyline.h; sourceTree = SOURCE_ROOT; };
		1125B2D41C4AD3DA007D0023 /* AIRMapPolyline.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapPolyline.m; path = AirMaps/AIRMapPolyline.m; sourceTree = SOURCE_ROOT; };
		1125B2D51C4AD3DA007D0023 /* AIRMapPolylineManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapPolylineManager.h; path = AirMaps/AIRMapPolylineManager.h; sourceTree = SOURCE_ROOT; };
		1125B2D61C4AD3DA007D0023 /* AIRMapPolylineManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapPolylineManager.m; path = AirMaps/AIRMapPolylineManager.m; sourceTree = SOURCE_ROOT; };
		1125B2F01C4AD445007D0023 /* SMCalloutView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SMCalloutView.h; path = AirMaps/Callout/SMCalloutView.h; sourceTree = SOURCE_ROOT; };
		1125B2F11C4AD445007D0023 /* SMCalloutView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SMCalloutView.m; path = AirMaps/Callout/SMCalloutView.m; sourceTree = SOURCE_ROOT; };
		11FA5C511C4A1296003AC2EE /* libAirMaps.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libAirMaps.a; sourceTree = BUILT_PRODUCTS_DIR; };
		19DABC7D1E7C9D3C00F41150 /* RCTConvert+AirMap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "RCTConvert+AirMap.h"; sourceTree = "<group>"; };
		19DABC7E1E7C9D3C00F41150 /* RCTConvert+AirMap.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "RCTConvert+AirMap.m"; sourceTree = "<group>"; };
		2163AA4E1FEAEDD100BBEC95 /* AIRMapPolylineRenderer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AIRMapPolylineRenderer.h; sourceTree = "<group>"; };
		2163AA4F1FEAEDD100BBEC95 /* AIRMapPolylineRenderer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AIRMapPolylineRenderer.m; sourceTree = "<group>"; };
		4C99C9DC2226CF2800A8693E /* AIRWeakTimerReference.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AIRWeakTimerReference.h; sourceTree = "<group>"; };
		4C99C9DD2226CF2800A8693E /* AIRWeakTimerReference.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AIRWeakTimerReference.m; sourceTree = "<group>"; };
		4C99C9DF2226D8C400A8693E /* AIRWeakMapReference.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AIRWeakMapReference.h; sourceTree = "<group>"; };
		4C99C9E02226D8C400A8693E /* AIRWeakMapReference.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AIRWeakMapReference.m; sourceTree = "<group>"; };
		4E0CFBDC2B388F2B0017E126 /* RCTComponentData+Maps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RCTComponentData+Maps.h"; sourceTree = "<group>"; };
		4E0CFBDD2B388F2B0017E126 /* RCTComponentData+Maps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "RCTComponentData+Maps.m"; sourceTree = "<group>"; };
		53D31635202E723B00B55447 /* AIRMapOverlayManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AIRMapOverlayManager.m; sourceTree = "<group>"; };
		53D31637202E725E00B55447 /* AIRMapOverlayManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AIRMapOverlayManager.h; sourceTree = "<group>"; };
		53D31638202E72D500B55447 /* AIRMapOverlay.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AIRMapOverlay.h; sourceTree = "<group>"; };
		53D31639202E72FC00B55447 /* AIRMapOverlay.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AIRMapOverlay.m; sourceTree = "<group>"; };
		53D3163B202E732300B55447 /* AIRMapOverlayRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AIRMapOverlayRenderer.h; sourceTree = "<group>"; };
		53D3163C202E734F00B55447 /* AIRMapOverlayRenderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AIRMapOverlayRenderer.m; sourceTree = "<group>"; };
		628F811E1FD16D780058313A /* AIRMapLocalTile.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AIRMapLocalTile.h; sourceTree = "<group>"; };
		628F811F1FD16DF80058313A /* AIRMapLocalTile.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AIRMapLocalTile.m; sourceTree = "<group>"; };
		628F81211FD16EAB0058313A /* AIRMapLocalTileManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AIRMapLocalTileManager.h; sourceTree = "<group>"; };
		628F81221FD16EFA0058313A /* AIRMapLocalTileManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AIRMapLocalTileManager.m; sourceTree = "<group>"; };
		62AEC4D31FD5A0AA003225E0 /* AIRMapLocalTileOverlay.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = AIRMapLocalTileOverlay.m; path = AirMaps/AIRMapLocalTileOverlay.m; sourceTree = "<group>"; };
		8B19A3C42257BBDE00BB8735 /* AIRMapCalloutSubview.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AIRMapCalloutSubview.m; sourceTree = "<group>"; };
		8B19A3C52257BBDE00BB8735 /* AIRMapCalloutSubviewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AIRMapCalloutSubviewManager.h; sourceTree = "<group>"; };
		8B19A3C62257BBDF00BB8735 /* AIRMapCalloutSubview.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AIRMapCalloutSubview.h; sourceTree = "<group>"; };
		8B19A3C72257BBDF00BB8735 /* AIRMapCalloutSubviewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AIRMapCalloutSubviewManager.m; sourceTree = "<group>"; };
		8BC85FAD2107C0BD0006CEA5 /* User.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = User.xcconfig; sourceTree = "<group>"; };
		8BC85FAE2107CFD80006CEA5 /* AIRGoogleMapOverlay.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapOverlay.h; path = AirGoogleMaps/AIRGoogleMapOverlay.h; sourceTree = "<group>"; };
		8BC85FAF2107CFEC0006CEA5 /* AIRGoogleMapOverlay.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapOverlay.m; path = AirGoogleMaps/AIRGoogleMapOverlay.m; sourceTree = "<group>"; };
		9B9498A42017EFB400158761 /* AIRGoogleMapCallout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapCallout.h; path = AirGoogleMaps/AIRGoogleMapCallout.h; sourceTree = "<group>"; };
		9B9498A52017EFB400158761 /* AIRGoogleMapPolygonManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapPolygonManager.h; path = AirGoogleMaps/AIRGoogleMapPolygonManager.h; sourceTree = "<group>"; };
		9B9498A62017EFB400158761 /* AIRGoogleMapUrlTile.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapUrlTile.m; path = AirGoogleMaps/AIRGoogleMapUrlTile.m; sourceTree = "<group>"; };
		9B9498A72017EFB400158761 /* AIRGoogleMapURLTileManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapURLTileManager.m; path = AirGoogleMaps/AIRGoogleMapURLTileManager.m; sourceTree = "<group>"; };
		9B9498A82017EFB400158761 /* AIRGMSPolygon.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGMSPolygon.m; path = AirGoogleMaps/AIRGMSPolygon.m; sourceTree = "<group>"; };
		9B9498A92017EFB400158761 /* RCTConvert+GMSMapViewType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "RCTConvert+GMSMapViewType.h"; path = "AirGoogleMaps/RCTConvert+GMSMapViewType.h"; sourceTree = "<group>"; };
		9B9498AA2017EFB400158761 /* AIRGoogleMap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMap.h; path = AirGoogleMaps/AIRGoogleMap.h; sourceTree = "<group>"; };
		9B9498AB2017EFB400158761 /* AIRGoogleMapMarkerManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapMarkerManager.h; path = AirGoogleMaps/AIRGoogleMapMarkerManager.h; sourceTree = "<group>"; };
		9B9498AC2017EFB400158761 /* AIRGoogleMapCallout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapCallout.m; path = AirGoogleMaps/AIRGoogleMapCallout.m; sourceTree = "<group>"; };
		9B9498AD2017EFB400158761 /* AIRGoogleMapUrlTileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapUrlTileManager.h; path = AirGoogleMaps/AIRGoogleMapUrlTileManager.h; sourceTree = "<group>"; };
		9B9498AE2017EFB500158761 /* AIRGMSMarker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGMSMarker.m; path = AirGoogleMaps/AIRGMSMarker.m; sourceTree = "<group>"; };
		9B9498AF2017EFB500158761 /* AIRGMSPolyline.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGMSPolyline.m; path = AirGoogleMaps/AIRGMSPolyline.m; sourceTree = "<group>"; };
		9B9498B02017EFB500158761 /* AIRGoogleMapCircleManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapCircleManager.h; path = AirGoogleMaps/AIRGoogleMapCircleManager.h; sourceTree = "<group>"; };
		9B9498B12017EFB500158761 /* AIRGoogleMapPolylineManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapPolylineManager.m; path = AirGoogleMaps/AIRGoogleMapPolylineManager.m; sourceTree = "<group>"; };
		9B9498B32017EFB500158761 /* AIRGoogleMapCircle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapCircle.m; path = AirGoogleMaps/AIRGoogleMapCircle.m; sourceTree = "<group>"; };
		9B9498B42017EFB500158761 /* AIRGMSPolyline.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGMSPolyline.h; path = AirGoogleMaps/AIRGMSPolyline.h; sourceTree = "<group>"; };
		9B9498B52017EFB500158761 /* AIRGoogleMapMarkerManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapMarkerManager.m; path = AirGoogleMaps/AIRGoogleMapMarkerManager.m; sourceTree = "<group>"; };
		9B9498B62017EFB500158761 /* AIRGoogleMapPolylineManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapPolylineManager.h; path = AirGoogleMaps/AIRGoogleMapPolylineManager.h; sourceTree = "<group>"; };
		9B9498B72017EFB500158761 /* AIRGoogleMap.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMap.m; path = AirGoogleMaps/AIRGoogleMap.m; sourceTree = "<group>"; };
		9B9498B82017EFB600158761 /* AIRGoogleMapPolyline.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapPolyline.h; path = AirGoogleMaps/AIRGoogleMapPolyline.h; sourceTree = "<group>"; };
		9B9498B92017EFB600158761 /* RCTConvert+GMSMapViewType.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "RCTConvert+GMSMapViewType.m"; path = "AirGoogleMaps/RCTConvert+GMSMapViewType.m"; sourceTree = "<group>"; };
		9B9498BA2017EFB600158761 /* AIRGMSPolygon.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGMSPolygon.h; path = AirGoogleMaps/AIRGMSPolygon.h; sourceTree = "<group>"; };
		9B9498BB2017EFB600158761 /* AIRGoogleMapPolyline.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapPolyline.m; path = AirGoogleMaps/AIRGoogleMapPolyline.m; sourceTree = "<group>"; };
		9B9498BC2017EFB600158761 /* AIRGoogleMapCircleManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapCircleManager.m; path = AirGoogleMaps/AIRGoogleMapCircleManager.m; sourceTree = "<group>"; };
		9B9498BD2017EFB600158761 /* AIRGoogleMapManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapManager.m; path = AirGoogleMaps/AIRGoogleMapManager.m; sourceTree = "<group>"; };
		9B9498BE2017EFB600158761 /* AIRGoogleMapManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapManager.h; path = AirGoogleMaps/AIRGoogleMapManager.h; sourceTree = "<group>"; };
		9B9498C02017EFB700158761 /* AIRGoogleMapMarker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapMarker.h; path = AirGoogleMaps/AIRGoogleMapMarker.h; sourceTree = "<group>"; };
		9B9498C12017EFB700158761 /* AIRGMSMarker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGMSMarker.h; path = AirGoogleMaps/AIRGMSMarker.h; sourceTree = "<group>"; };
		9B9498C22017EFB700158761 /* AIRGoogleMapCircle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapCircle.h; path = AirGoogleMaps/AIRGoogleMapCircle.h; sourceTree = "<group>"; };
		9B9498C32017EFB700158761 /* AIRGoogleMapPolygon.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapPolygon.h; path = AirGoogleMaps/AIRGoogleMapPolygon.h; sourceTree = "<group>"; };
		9B9498C42017EFB700158761 /* AIRGoogleMapCalloutManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapCalloutManager.m; path = AirGoogleMaps/AIRGoogleMapCalloutManager.m; sourceTree = "<group>"; };
		9B9498C52017EFB800158761 /* AIRGoogleMapCalloutManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapCalloutManager.h; path = AirGoogleMaps/AIRGoogleMapCalloutManager.h; sourceTree = "<group>"; };
		9B9498C62017EFB800158761 /* AIRGoogleMapPolygon.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapPolygon.m; path = AirGoogleMaps/AIRGoogleMapPolygon.m; sourceTree = "<group>"; };
		9B9498C72017EFB800158761 /* AIRGoogleMapMarker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapMarker.m; path = AirGoogleMaps/AIRGoogleMapMarker.m; sourceTree = "<group>"; };
		9B9498C82017EFB800158761 /* AIRGoogleMapUrlTile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapUrlTile.h; path = AirGoogleMaps/AIRGoogleMapUrlTile.h; sourceTree = "<group>"; };
		9B9498C92017EFB800158761 /* AIRGoogleMapPolygonManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapPolygonManager.m; path = AirGoogleMaps/AIRGoogleMapPolygonManager.m; sourceTree = "<group>"; };
		A8494E24218891020092506D /* AIRMapWMSTileManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AIRMapWMSTileManager.m; sourceTree = "<group>"; };
		A8494E25218891020092506D /* AIRMapWMSTile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AIRMapWMSTile.h; sourceTree = "<group>"; };
		A8494E26218891020092506D /* AIRMapWMSTileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AIRMapWMSTileManager.h; sourceTree = "<group>"; };
		A8494E27218891020092506D /* AIRMapWMSTile.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AIRMapWMSTile.m; sourceTree = "<group>"; };
		A8494E2A218891180092506D /* AIRGoogleMapWMSTileManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapWMSTileManager.m; path = AirGoogleMaps/AIRGoogleMapWMSTileManager.m; sourceTree = "<group>"; };
		A8494E2B218891180092506D /* AIRGoogleMapWMSTile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapWMSTile.h; path = AirGoogleMaps/AIRGoogleMapWMSTile.h; sourceTree = "<group>"; };
		A8494E2C218891180092506D /* AIRGoogleMapWMSTileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRGoogleMapWMSTileManager.h; path = AirGoogleMaps/AIRGoogleMapWMSTileManager.h; sourceTree = "<group>"; };
		A8494E2D218891180092506D /* AIRGoogleMapWMSTile.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRGoogleMapWMSTile.m; path = AirGoogleMaps/AIRGoogleMapWMSTile.m; sourceTree = "<group>"; };
		B5EA3BA72098E22B000E7AFD /* AIRDummyView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRDummyView.m; path = AirGoogleMaps/AIRDummyView.m; sourceTree = "<group>"; };
		B5EA3BA82098E22B000E7AFD /* AIRDummyView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRDummyView.h; path = AirGoogleMaps/AIRDummyView.h; sourceTree = "<group>"; };
		BE2E4EC72621F63C00CC7F2E /* AIRMapUrlTileCachedOverlay.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AIRMapUrlTileCachedOverlay.h; path = AirMaps/AIRMapUrlTileCachedOverlay.h; sourceTree = SOURCE_ROOT; };
		BE2E4EC82621F63C00CC7F2E /* AIRMapUrlTileCachedOverlay.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AIRMapUrlTileCachedOverlay.m; path = AirMaps/AIRMapUrlTileCachedOverlay.m; sourceTree = SOURCE_ROOT; };
		DA6C26361C9E2AFE0035349F /* AIRMapUrlTile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AIRMapUrlTile.h; sourceTree = "<group>"; };
		DA6C26371C9E2AFE0035349F /* AIRMapUrlTile.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AIRMapUrlTile.m; sourceTree = "<group>"; };
		DA6C263C1C9E324A0035349F /* AIRMapUrlTileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AIRMapUrlTileManager.h; sourceTree = "<group>"; };
		DA6C263D1C9E324A0035349F /* AIRMapUrlTileManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AIRMapUrlTileManager.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		11FA5C4E1C4A1296003AC2EE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		11FA5C481C4A1296003AC2EE = {
			isa = PBXGroup;
			children = (
				9B9498A32017EF9D00158761 /* AirGoogleMaps */,
				62AEC4D31FD5A0AA003225E0 /* AIRMapLocalTileOverlay.m */,
				11FA5C531C4A1296003AC2EE /* AirMaps */,
				11FA5C521C4A1296003AC2EE /* Products */,
				8BC85FAD2107C0BD0006CEA5 /* User.xcconfig */,
			);
			sourceTree = "<group>";
		};
		11FA5C521C4A1296003AC2EE /* Products */ = {
			isa = PBXGroup;
			children = (
				11FA5C511C4A1296003AC2EE /* libAirMaps.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		11FA5C531C4A1296003AC2EE /* AirMaps */ = {
			isa = PBXGroup;
			children = (
				4E0CFBDC2B388F2B0017E126 /* RCTComponentData+Maps.h */,
				4E0CFBDD2B388F2B0017E126 /* RCTComponentData+Maps.m */,
				BE2E4EC72621F63C00CC7F2E /* AIRMapUrlTileCachedOverlay.h */,
				BE2E4EC82621F63C00CC7F2E /* AIRMapUrlTileCachedOverlay.m */,
				1125B2BD1C4AD3DA007D0023 /* AIRMap.h */,
				1125B2BE1C4AD3DA007D0023 /* AIRMap.m */,
				1125B2BF1C4AD3DA007D0023 /* AIRMapCallout.h */,
				1125B2C01C4AD3DA007D0023 /* AIRMapCallout.m */,
				1125B2C11C4AD3DA007D0023 /* AIRMapCalloutManager.h */,
				1125B2C21C4AD3DA007D0023 /* AIRMapCalloutManager.m */,
				8B19A3C62257BBDF00BB8735 /* AIRMapCalloutSubview.h */,
				8B19A3C42257BBDE00BB8735 /* AIRMapCalloutSubview.m */,
				8B19A3C52257BBDE00BB8735 /* AIRMapCalloutSubviewManager.h */,
				8B19A3C72257BBDF00BB8735 /* AIRMapCalloutSubviewManager.m */,
				1125B2C31C4AD3DA007D0023 /* AIRMapCircle.h */,
				1125B2C41C4AD3DA007D0023 /* AIRMapCircle.m */,
				1125B2C51C4AD3DA007D0023 /* AIRMapCircleManager.h */,
				1125B2C61C4AD3DA007D0023 /* AIRMapCircleManager.m */,
				1125B2D41C4AD3DA007D0023 /* AIRMapPolyline.m */,
				1125B2C71C4AD3DA007D0023 /* AIRMapCoordinate.h */,
				1125B2C81C4AD3DA007D0023 /* AIRMapCoordinate.m */,
				1125B2C91C4AD3DA007D0023 /* AIRMapManager.h */,
				1125B2CA1C4AD3DA007D0023 /* AIRMapManager.m */,
				1125B2CB1C4AD3DA007D0023 /* AIRMapMarker.h */,
				1125B2CC1C4AD3DA007D0023 /* AIRMapMarker.m */,
				1125B2CD1C4AD3DA007D0023 /* AIRMapMarkerManager.h */,
				1125B2CE1C4AD3DA007D0023 /* AIRMapMarkerManager.m */,
				1125B2CF1C4AD3DA007D0023 /* AIRMapPolygon.h */,
				1125B2D01C4AD3DA007D0023 /* AIRMapPolygon.m */,
				1125B2D11C4AD3DA007D0023 /* AIRMapPolygonManager.h */,
				1125B2D21C4AD3DA007D0023 /* AIRMapPolygonManager.m */,
				1125B2D31C4AD3DA007D0023 /* AIRMapPolyline.h */,
				1125B2D51C4AD3DA007D0023 /* AIRMapPolylineManager.h */,
				1125B2D61C4AD3DA007D0023 /* AIRMapPolylineManager.m */,
				2163AA4E1FEAEDD100BBEC95 /* AIRMapPolylineRenderer.h */,
				2163AA4F1FEAEDD100BBEC95 /* AIRMapPolylineRenderer.m */,
				1125B2F01C4AD445007D0023 /* SMCalloutView.h */,
				1125B2F11C4AD445007D0023 /* SMCalloutView.m */,
				19DABC7D1E7C9D3C00F41150 /* RCTConvert+AirMap.h */,
				19DABC7E1E7C9D3C00F41150 /* RCTConvert+AirMap.m */,
				DA6C26361C9E2AFE0035349F /* AIRMapUrlTile.h */,
				DA6C26371C9E2AFE0035349F /* AIRMapUrlTile.m */,
				DA6C263C1C9E324A0035349F /* AIRMapUrlTileManager.h */,
				DA6C263D1C9E324A0035349F /* AIRMapUrlTileManager.m */,
				A8494E25218891020092506D /* AIRMapWMSTile.h */,
				A8494E27218891020092506D /* AIRMapWMSTile.m */,
				A8494E26218891020092506D /* AIRMapWMSTileManager.h */,
				A8494E24218891020092506D /* AIRMapWMSTileManager.m */,
				1125B2CC1C4AD3DA007D0023 /* AIRMapMarker.m */,
				628F811E1FD16D780058313A /* AIRMapLocalTile.h */,
				628F811F1FD16DF80058313A /* AIRMapLocalTile.m */,
				628F81211FD16EAB0058313A /* AIRMapLocalTileManager.h */,
				628F81221FD16EFA0058313A /* AIRMapLocalTileManager.m */,
				53D31638202E72D500B55447 /* AIRMapOverlay.h */,
				53D31639202E72FC00B55447 /* AIRMapOverlay.m */,
				53D31637202E725E00B55447 /* AIRMapOverlayManager.h */,
				53D31635202E723B00B55447 /* AIRMapOverlayManager.m */,
				53D3163B202E732300B55447 /* AIRMapOverlayRenderer.h */,
				53D3163C202E734F00B55447 /* AIRMapOverlayRenderer.m */,
				4C99C9DC2226CF2800A8693E /* AIRWeakTimerReference.h */,
				4C99C9DD2226CF2800A8693E /* AIRWeakTimerReference.m */,
				4C99C9DF2226D8C400A8693E /* AIRWeakMapReference.h */,
				4C99C9E02226D8C400A8693E /* AIRWeakMapReference.m */,
			);
			path = AirMaps;
			sourceTree = "<group>";
		};
		9B9498A32017EF9D00158761 /* AirGoogleMaps */ = {
			isa = PBXGroup;
			children = (
				B5EA3BA82098E22B000E7AFD /* AIRDummyView.h */,
				B5EA3BA72098E22B000E7AFD /* AIRDummyView.m */,
				9B9498C12017EFB700158761 /* AIRGMSMarker.h */,
				9B9498AE2017EFB500158761 /* AIRGMSMarker.m */,
				9B9498BA2017EFB600158761 /* AIRGMSPolygon.h */,
				9B9498A82017EFB400158761 /* AIRGMSPolygon.m */,
				9B9498B42017EFB500158761 /* AIRGMSPolyline.h */,
				9B9498AF2017EFB500158761 /* AIRGMSPolyline.m */,
				9B9498AA2017EFB400158761 /* AIRGoogleMap.h */,
				9B9498B72017EFB500158761 /* AIRGoogleMap.m */,
				9B9498A42017EFB400158761 /* AIRGoogleMapCallout.h */,
				9B9498AC2017EFB400158761 /* AIRGoogleMapCallout.m */,
				9B9498C52017EFB800158761 /* AIRGoogleMapCalloutManager.h */,
				9B9498C42017EFB700158761 /* AIRGoogleMapCalloutManager.m */,
				9B9498C22017EFB700158761 /* AIRGoogleMapCircle.h */,
				9B9498B32017EFB500158761 /* AIRGoogleMapCircle.m */,
				9B9498B02017EFB500158761 /* AIRGoogleMapCircleManager.h */,
				9B9498BC2017EFB600158761 /* AIRGoogleMapCircleManager.m */,
				9B9498BE2017EFB600158761 /* AIRGoogleMapManager.h */,
				9B9498BD2017EFB600158761 /* AIRGoogleMapManager.m */,
				9B9498C02017EFB700158761 /* AIRGoogleMapMarker.h */,
				9B9498C72017EFB800158761 /* AIRGoogleMapMarker.m */,
				9B9498AB2017EFB400158761 /* AIRGoogleMapMarkerManager.h */,
				9B9498B52017EFB500158761 /* AIRGoogleMapMarkerManager.m */,
				9B9498C32017EFB700158761 /* AIRGoogleMapPolygon.h */,
				9B9498C62017EFB800158761 /* AIRGoogleMapPolygon.m */,
				9B9498A52017EFB400158761 /* AIRGoogleMapPolygonManager.h */,
				9B9498C92017EFB800158761 /* AIRGoogleMapPolygonManager.m */,
				9B9498B82017EFB600158761 /* AIRGoogleMapPolyline.h */,
				9B9498BB2017EFB600158761 /* AIRGoogleMapPolyline.m */,
				8BC85FAE2107CFD80006CEA5 /* AIRGoogleMapOverlay.h */,
				8BC85FAF2107CFEC0006CEA5 /* AIRGoogleMapOverlay.m */,
				9B9498B62017EFB500158761 /* AIRGoogleMapPolylineManager.h */,
				9B9498B12017EFB500158761 /* AIRGoogleMapPolylineManager.m */,
				9B9498C82017EFB800158761 /* AIRGoogleMapUrlTile.h */,
				9B9498A62017EFB400158761 /* AIRGoogleMapUrlTile.m */,
				9B9498AD2017EFB400158761 /* AIRGoogleMapUrlTileManager.h */,
				9B9498A72017EFB400158761 /* AIRGoogleMapURLTileManager.m */,
				A8494E2B218891180092506D /* AIRGoogleMapWMSTile.h */,
				A8494E2D218891180092506D /* AIRGoogleMapWMSTile.m */,
				A8494E2C218891180092506D /* AIRGoogleMapWMSTileManager.h */,
				A8494E2A218891180092506D /* AIRGoogleMapWMSTileManager.m */,
				9B9498A92017EFB400158761 /* RCTConvert+GMSMapViewType.h */,
				9B9498B92017EFB600158761 /* RCTConvert+GMSMapViewType.m */,
			);
			name = AirGoogleMaps;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		11FA5C501C4A1296003AC2EE /* AirMaps */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 11FA5C5A1C4A1296003AC2EE /* Build configuration list for PBXNativeTarget "AirMaps" */;
			buildPhases = (
				11FA5C4D1C4A1296003AC2EE /* Sources */,
				11FA5C4E1C4A1296003AC2EE /* Frameworks */,
				11FA5C4F1C4A1296003AC2EE /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AirMaps;
			productName = AirMaps;
			productReference = 11FA5C511C4A1296003AC2EE /* libAirMaps.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		11FA5C491C4A1296003AC2EE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0940;
				ORGANIZATIONNAME = Christopher;
				TargetAttributes = {
					11FA5C501C4A1296003AC2EE = {
						CreatedOnToolsVersion = 7.2;
					};
				};
			};
			buildConfigurationList = 11FA5C4C1C4A1296003AC2EE /* Build configuration list for PBXProject "AirMaps" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 11FA5C481C4A1296003AC2EE;
			productRefGroup = 11FA5C521C4A1296003AC2EE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				11FA5C501C4A1296003AC2EE /* AirMaps */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		11FA5C4D1C4A1296003AC2EE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				62AEC4D41FD5A0AA003225E0 /* AIRMapLocalTileOverlay.m in Sources */,
				9B9498DC2017EFB800158761 /* AIRGoogleMapPolygonManager.m in Sources */,
				1125B2E31C4AD3DA007D0023 /* AIRMapPolygon.m in Sources */,
				4C99C9DE2226CF2800A8693E /* AIRWeakTimerReference.m in Sources */,
				1125B2E41C4AD3DA007D0023 /* AIRMapPolygonManager.m in Sources */,
				A8494E2F218891180092506D /* AIRGoogleMapWMSTile.m in Sources */,
				9B9498CB2017EFB800158761 /* AIRGoogleMapURLTileManager.m in Sources */,
				A8494E28218891020092506D /* AIRMapWMSTileManager.m in Sources */,
				1125B2DB1C4AD3DA007D0023 /* AIRMapCallout.m in Sources */,
				53D31636202E723B00B55447 /* AIRMapOverlayManager.m in Sources */,
				4E0CFBDE2B388F2B0017E126 /* RCTComponentData+Maps.m in Sources */,
				1125B2E01C4AD3DA007D0023 /* AIRMapManager.m in Sources */,
				1125B2E61C4AD3DA007D0023 /* AIRMapPolylineManager.m in Sources */,
				9B9498DA2017EFB800158761 /* AIRGoogleMapPolygon.m in Sources */,
				9B9498D12017EFB800158761 /* AIRGoogleMapCircle.m in Sources */,
				1125B2DD1C4AD3DA007D0023 /* AIRMapCircle.m in Sources */,
				9B9498CE2017EFB800158761 /* AIRGMSMarker.m in Sources */,
				9B9498D72017EFB800158761 /* AIRGoogleMapManager.m in Sources */,
				19DABC7F1E7C9D3C00F41150 /* RCTConvert+AirMap.m in Sources */,
				A8494E2E218891180092506D /* AIRGoogleMapWMSTileManager.m in Sources */,
				8B19A3C82257BBDF00BB8735 /* AIRMapCalloutSubview.m in Sources */,
				1125B2E51C4AD3DA007D0023 /* AIRMapPolyline.m in Sources */,
				4C99C9E12226D8C400A8693E /* AIRWeakMapReference.m in Sources */,
				9B9498D52017EFB800158761 /* AIRGoogleMapPolyline.m in Sources */,
				9B9498CF2017EFB800158761 /* AIRGMSPolyline.m in Sources */,
				9B9498D42017EFB800158761 /* RCTConvert+GMSMapViewType.m in Sources */,
				9B9498D32017EFB800158761 /* AIRGoogleMap.m in Sources */,
				BE2E4EC92621F63C00CC7F2E /* AIRMapUrlTileCachedOverlay.m in Sources */,
				DA6C263E1C9E324A0035349F /* AIRMapUrlTileManager.m in Sources */,
				9B9498DB2017EFB800158761 /* AIRGoogleMapMarker.m in Sources */,
				628F81201FD16DF80058313A /* AIRMapLocalTile.m in Sources */,
				9B9498D92017EFB800158761 /* AIRGoogleMapCalloutManager.m in Sources */,
				53D3163A202E72FC00B55447 /* AIRMapOverlay.m in Sources */,
				53D3163D202E734F00B55447 /* AIRMapOverlayRenderer.m in Sources */,
				1125B2DA1C4AD3DA007D0023 /* AIRMap.m in Sources */,
				1125B2DF1C4AD3DA007D0023 /* AIRMapCoordinate.m in Sources */,
				9B9498D62017EFB800158761 /* AIRGoogleMapCircleManager.m in Sources */,
				8BC85FB02107CFEC0006CEA5 /* AIRGoogleMapOverlay.m in Sources */,
				1125B2F21C4AD445007D0023 /* SMCalloutView.m in Sources */,
				2163AA501FEAEDD100BBEC95 /* AIRMapPolylineRenderer.m in Sources */,
				9B9498D02017EFB800158761 /* AIRGoogleMapPolylineManager.m in Sources */,
				1125B2E11C4AD3DA007D0023 /* AIRMapMarker.m in Sources */,
				A8494E29218891020092506D /* AIRMapWMSTile.m in Sources */,
				9B9498CA2017EFB800158761 /* AIRGoogleMapUrlTile.m in Sources */,
				8B19A3C92257BBDF00BB8735 /* AIRMapCalloutSubviewManager.m in Sources */,
				B5EA3BA92098E22B000E7AFD /* AIRDummyView.m in Sources */,
				9B9498CD2017EFB800158761 /* AIRGoogleMapCallout.m in Sources */,
				1125B2E21C4AD3DA007D0023 /* AIRMapMarkerManager.m in Sources */,
				DA6C26381C9E2AFE0035349F /* AIRMapUrlTile.m in Sources */,
				628F81231FD16EFA0058313A /* AIRMapLocalTileManager.m in Sources */,
				9B9498D22017EFB800158761 /* AIRGoogleMapMarkerManager.m in Sources */,
				9B9498CC2017EFB800158761 /* AIRGMSPolygon.m in Sources */,
				1125B2DE1C4AD3DA007D0023 /* AIRMapCircleManager.m in Sources */,
				1125B2DC1C4AD3DA007D0023 /* AIRMapCalloutManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		11FA5C581C4A1296003AC2EE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = "";
				IPHONEOS_DEPLOYMENT_TARGET = 9.2;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		11FA5C591C4A1296003AC2EE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = "";
				IPHONEOS_DEPLOYMENT_TARGET = 9.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		11FA5C5B1C4A1296003AC2EE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8BC85FAD2107C0BD0006CEA5 /* User.xcconfig */;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../react-native/Libraries/Image",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		11FA5C5C1C4A1296003AC2EE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8BC85FAD2107C0BD0006CEA5 /* User.xcconfig */;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../react-native/Libraries/Image",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		11FA5C4C1C4A1296003AC2EE /* Build configuration list for PBXProject "AirMaps" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				11FA5C581C4A1296003AC2EE /* Debug */,
				11FA5C591C4A1296003AC2EE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		11FA5C5A1C4A1296003AC2EE /* Build configuration list for PBXNativeTarget "AirMaps" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				11FA5C5B1C4A1296003AC2EE /* Debug */,
				11FA5C5C1C4A1296003AC2EE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 11FA5C491C4A1296003AC2EE /* Project object */;
}
