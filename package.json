{"name": "steppr", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@supabase/supabase-js": "^2.39.3", "expo": "~50.0.5", "expo-barcode-scanner": "~12.9.2", "expo-camera": "~14.1.3", "expo-constants": "~15.4.5", "expo-device": "~5.9.3", "expo-notifications": "~0.27.6", "expo-secure-store": "~12.8.1", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-qrcode-svg": "^6.2.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-native-svg-transformer": "^1.3.0", "react-native-url-polyfill": "^2.0.0", "expo-location": "~16.5.5", "react-native-maps": "1.10.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "typescript": "^5.1.3"}, "private": true}