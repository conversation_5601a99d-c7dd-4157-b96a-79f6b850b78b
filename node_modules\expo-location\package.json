{"name": "expo-location", "version": "16.5.5", "description": "Allows reading geolocation information from the device. Your app can poll for the current location or subscribe to location update events.", "main": "build/Location.js", "types": "build/Location.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "location", "geolocation", "coords", "geocoding", "compass", "heading"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-location"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/location/", "jest": {"preset": "expo-module-scripts"}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "01bef8630cd9fd913bb465afd010d6030a9ca38f"}