import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import { Subscription } from 'expo-modules-core';
import { useNavigation } from '@react-navigation/native';
import notificationService, { PushNotification } from '../services/NotificationService';
import { useAuth } from './AuthContext';

type NotificationPreferences = {
  eventReminders: boolean;
  ticketUpdates: boolean;
  marketing: boolean;
};

type NotificationContextType = {
  pushToken: string | null;
  notificationCount: number;
  notificationPreferences: NotificationPreferences;
  registerForPushNotifications: () => Promise<void>;
  sendLocalNotification: (notification: PushNotification) => Promise<void>;
  clearNotifications: () => Promise<void>;
  updateNotificationPreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>;
};

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [pushToken, setPushToken] = useState<string | null>(null);
  const [notificationCount, setNotificationCount] = useState(0);
  const [notificationPreferences, setNotificationPreferences] = useState<NotificationPreferences>({
    eventReminders: true,
    ticketUpdates: true,
    marketing: false,
  });
  
  const notificationListener = useRef<Subscription>();
  const responseListener = useRef<Subscription>();
  const navigation = useNavigation();
  const { user } = useAuth();

  useEffect(() => {
    registerForPushNotifications();

    // Set up notification listeners
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      setNotificationCount(prev => prev + 1);
    });

    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      
      // Handle notification tap
      if (data.type === 'event') {
        // @ts-ignore
        navigation.navigate('EventDetail', { eventId: data.eventId });
      } else if (data.type === 'ticket') {
        // @ts-ignore
        navigation.navigate('TicketDetail', { ticketId: data.ticketId });
      }
    });

    // Clean up listeners on unmount
    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  // Fetch notification preferences when user changes
  useEffect(() => {
    if (user) {
      fetchNotificationPreferences();
    }
  }, [user]);

  const registerForPushNotifications = async () => {
    try {
      const token = await notificationService.registerForPushNotifications();
      setPushToken(token);
    } catch (error) {
      console.error('Error registering for push notifications:', error);
    }
  };

  const sendLocalNotification = async (notification: PushNotification) => {
    try {
      await notificationService.scheduleLocalNotification(notification);
      setNotificationCount(prev => prev + 1);
    } catch (error) {
      console.error('Error sending local notification:', error);
    }
  };

  const clearNotifications = async () => {
    try {
      await notificationService.cancelAllNotifications();
      setNotificationCount(0);
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  };

  const fetchNotificationPreferences = async () => {
    // In a real app, this would fetch from Supabase
    // For now, we'll use the default values
    setNotificationPreferences({
      eventReminders: true,
      ticketUpdates: true,
      marketing: false,
    });
  };

  const updateNotificationPreferences = async (preferences: Partial<NotificationPreferences>) => {
    try {
      // In a real app, this would update Supabase
      setNotificationPreferences(prev => ({
        ...prev,
        ...preferences,
      }));
    } catch (error) {
      console.error('Error updating notification preferences:', error);
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        pushToken,
        notificationCount,
        notificationPreferences,
        registerForPushNotifications,
        sendLocalNotification,
        clearNotifications,
        updateNotificationPreferences,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
