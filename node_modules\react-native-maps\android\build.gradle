def safeExtGet(prop, fallback) {
  rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

buildscript {
  // The Android Gradle plugin is only required when opening the android folder stand-alone.
  // This avoids unnecessary downloads and potential conflicts when the library is included as a
  // module dependency in an application project.
  if (project == rootProject) {
    repositories {
      google()
      mavenCentral()
    }
    dependencies {
      classpath("com.android.tools.build:gradle:7.6.3")
    }
  }
}

apply plugin: 'com.android.library'

android {
  def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
  if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
    namespace "com.rnmaps.maps"
  }

  compileSdk safeExtGet('compileSdkVersion', 34)

  defaultConfig {
    minSdkVersion safeExtGet('minSdkVersion', 21)
    targetSdkVersion safeExtGet('targetSdkVersion', 34)
  }
}

repositories {
  mavenLocal()
  mavenCentral()
  maven {
    // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
    url "$rootDir/../node_modules/react-native/android"
  }
  maven {
    // Android JSC is installed from npm
    url "$rootDir/../node_modules/jsc-android/dist"
  }
  google()
}

dependencies {
  implementation 'com.facebook.react:react-native:+'
  implementation "com.google.android.gms:play-services-base:${safeExtGet('playServicesVersion', '18.2.0')}"
  implementation "com.google.android.gms:play-services-maps:${safeExtGet('playServicesVersion', '18.2.0')}"
  implementation "com.google.android.gms:play-services-location:21.0.1"
  implementation 'com.google.maps.android:android-maps-utils:3.8.2'
  implementation "androidx.work:work-runtime:2.7.1"
}
