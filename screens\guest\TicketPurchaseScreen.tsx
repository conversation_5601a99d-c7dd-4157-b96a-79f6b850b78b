import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { ExploreStackParamList } from '../../navigation/GuestNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../../components/Button';
import { supabase } from '../../supabase';

type Props = NativeStackScreenProps<ExploreStackParamList, 'TicketPurchase'>;

// Mock event data
const mockEvent = {
  id: '1',
  title: 'Summer Beach Party',
  date: 'June 15, 2023',
  time: '8:00 PM - 2:00 AM',
  location: 'Elegushi Beach, Lagos',
  imageUrl: 'https://images.unsplash.com/photo-*************-7a4b6ad7a6c3',
  ticketTypes: [
    { id: '1', name: 'Regular', price: 5000, description: 'General admission' },
    { id: '2', name: 'VIP', price: 15000, description: 'Premium area access' },
    { id: '3', name: 'Table', price: 50000, description: 'Reserved table for 6' },
  ],
};

// Mock payment methods
const paymentMethods = [
  { id: '1', name: 'Credit/Debit Card', icon: 'card-outline' },
  { id: '2', name: 'Bank Transfer', icon: 'cash-outline' },
  { id: '3', name: 'Pay at Vendor', icon: 'storefront-outline' },
];

export default function TicketPurchaseScreen({ route, navigation }: Props) {
  const { eventId } = route.params;
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [selectedTicket, setSelectedTicket] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedPayment, setSelectedPayment] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  
  // In a real app, we would fetch the event details from Supabase here
  const event = mockEvent;

  const getSelectedTicketType = () => {
    return event.ticketTypes.find(ticket => ticket.id === selectedTicket);
  };

  const getTotalPrice = () => {
    const ticket = getSelectedTicketType();
    return ticket ? ticket.price * quantity : 0;
  };

  const handlePurchase = async () => {
    if (!selectedTicket) {
      Alert.alert('Error', 'Please select a ticket type');
      return;
    }

    if (!selectedPayment) {
      Alert.alert('Error', 'Please select a payment method');
      return;
    }

    try {
      setLoading(true);
      
      // In a real app, we would process the payment and create tickets in Supabase here
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Success
      Alert.alert(
        'Purchase Successful',
        'Your tickets have been added to your STEPPS',
        [
          {
            text: 'View Tickets',
            onPress: () => navigation.navigate('TicketsStack' as any),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to purchase tickets. Please try again.');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.eventInfo}>
        <Text style={[styles.eventTitle, { color: theme.colors.text }]}>
          {event.title}
        </Text>
        <Text style={[styles.eventDetails, { color: theme.colors.text + '80' }]}>
          {event.date} • {event.time}
        </Text>
        <Text style={[styles.eventDetails, { color: theme.colors.text + '80' }]}>
          {event.location}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Select Ticket Type
        </Text>
        
        {event.ticketTypes.map((ticket) => (
          <TouchableOpacity
            key={ticket.id}
            style={[
              styles.ticketItem,
              {
                backgroundColor: theme.colors.card,
                borderColor:
                  selectedTicket === ticket.id
                    ? theme.colors.primary
                    : theme.colors.border,
              },
            ]}
            onPress={() => setSelectedTicket(ticket.id)}
          >
            <View style={styles.ticketInfo}>
              <Text style={[styles.ticketName, { color: theme.colors.text }]}>
                {ticket.name}
              </Text>
              <Text style={[styles.ticketDescription, { color: theme.colors.text + '80' }]}>
                {ticket.description}
              </Text>
            </View>
            <View style={styles.ticketPriceContainer}>
              <Text style={[styles.ticketPrice, { color: theme.colors.primary }]}>
                ₦{ticket.price.toLocaleString()}
              </Text>
              {selectedTicket === ticket.id && (
                <Ionicons name="checkmark-circle" size={24} color={theme.colors.primary} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {selectedTicket && (
        <>
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Quantity
            </Text>
            
            <View style={styles.quantityContainer}>
              <TouchableOpacity
                style={[
                  styles.quantityButton,
                  {
                    backgroundColor: theme.colors.card,
                    borderColor: theme.colors.border,
                  },
                ]}
                onPress={() => setQuantity(Math.max(1, quantity - 1))}
              >
                <Ionicons name="remove" size={24} color={theme.colors.text} />
              </TouchableOpacity>
              
              <Text style={[styles.quantityText, { color: theme.colors.text }]}>
                {quantity}
              </Text>
              
              <TouchableOpacity
                style={[
                  styles.quantityButton,
                  {
                    backgroundColor: theme.colors.card,
                    borderColor: theme.colors.border,
                  },
                ]}
                onPress={() => setQuantity(quantity + 1)}
              >
                <Ionicons name="add" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Payment Method
            </Text>
            
            {paymentMethods.map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.paymentItem,
                  {
                    backgroundColor: theme.colors.card,
                    borderColor:
                      selectedPayment === method.id
                        ? theme.colors.primary
                        : theme.colors.border,
                  },
                ]}
                onPress={() => setSelectedPayment(method.id)}
              >
                <View style={styles.paymentInfo}>
                  <Ionicons name={method.icon as any} size={24} color={theme.colors.primary} />
                  <Text style={[styles.paymentName, { color: theme.colors.text }]}>
                    {method.name}
                  </Text>
                </View>
                {selectedPayment === method.id && (
                  <Ionicons name="checkmark-circle" size={24} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.summarySection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Order Summary
            </Text>
            
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryItemText, { color: theme.colors.text }]}>
                {getSelectedTicketType()?.name} Ticket x {quantity}
              </Text>
              <Text style={[styles.summaryItemValue, { color: theme.colors.text }]}>
                ₦{(getSelectedTicketType()?.price || 0).toLocaleString()} x {quantity}
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryItemText, { color: theme.colors.text }]}>
                Service Fee
              </Text>
              <Text style={[styles.summaryItemValue, { color: theme.colors.text }]}>
                ₦0
              </Text>
            </View>
            
            <View style={[styles.totalItem, { borderTopColor: theme.colors.border }]}>
              <Text style={[styles.totalText, { color: theme.colors.text }]}>
                Total
              </Text>
              <Text style={[styles.totalValue, { color: theme.colors.primary }]}>
                ₦{getTotalPrice().toLocaleString()}
              </Text>
            </View>
          </View>
        </>
      )}

      <View style={styles.footer}>
        <Button
          title={loading ? 'Processing...' : 'Complete Purchase'}
          onPress={handlePurchase}
          disabled={!selectedTicket || !selectedPayment || loading}
          loading={loading}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  eventInfo: {
    marginBottom: 20,
  },
  eventTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  eventDetails: {
    fontSize: 14,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  ticketItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    borderWidth: 2,
    marginBottom: 10,
  },
  ticketInfo: {
    flex: 1,
  },
  ticketName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  ticketDescription: {
    fontSize: 14,
  },
  ticketPriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ticketPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 10,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 20,
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    borderWidth: 2,
    marginBottom: 10,
  },
  paymentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentName: {
    fontSize: 16,
    marginLeft: 10,
  },
  summarySection: {
    marginBottom: 30,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  summaryItemText: {
    fontSize: 14,
  },
  summaryItemValue: {
    fontSize: 14,
  },
  totalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 15,
    borderTopWidth: 1,
    marginTop: 10,
  },
  totalText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  footer: {
    marginBottom: 30,
  },
});
