import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { HostProfileStackParamList } from '../../navigation/HostNavigator';
import { useTheme } from '../../contexts/ThemeContext';

type Props = NativeStackScreenProps<HostProfileStackParamList, 'HostProfile'>;

export default function HostProfileScreen() {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.text, { color: theme.colors.text }]}>
        Host Profile Screen
      </Text>
      <Text style={[styles.text, { color: theme.colors.text }]}>
        This screen will show the host's profile information.
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
});
