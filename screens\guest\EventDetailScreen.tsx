import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { ExploreStackParamList } from '../../navigation/GuestNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import Button from '../../components/Button';
import MapView, { Marker } from 'react-native-maps';
import * as Location from 'expo-location';

type Props = NativeStackScreenProps<ExploreStackParamList, 'EventDetail'>;

const { width } = Dimensions.get('window');

// Mock event data
const mockEvent = {
  id: '1',
  title: 'Summer Beach Party',
  date: 'June 15, 2023',
  time: '8:00 PM - 2:00 AM',
  location: 'Elegushi Beach, Lagos',
  description:
    'Join us for the ultimate summer beach party! Featuring top DJs, live performances, food, drinks, and more. Dress code: Beach casual.',
  imageUrl: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3',
  organizer: 'Beach Vibes Entertainment',
  ticketTypes: [
    { id: '1', name: 'Regular', price: '₦5,000' },
    { id: '2', name: 'VIP', price: '₦15,000' },
    { id: '3', name: 'Table', price: '₦50,000' },
  ],
};

export default function EventDetailScreen({ route, navigation }: Props) {
  const { eventId } = route.params;
  const { theme } = useTheme();
  const [resaleTickets, setResaleTickets] = useState<{id: string, type: string, price: string}[]>([]);
  const [sharing, setSharing] = useState(false);

  // Hardcoded event coordinates for Elegushi Beach, Lagos (replace with real event data)
  const eventCoords = { latitude: 6.4241, longitude: 3.4645 };

  // In a real app, we would fetch the event details from Supabase here
  const event = mockEvent;

  // Dummy fetch (replace with backend call)
  useEffect(() => {
    // TODO: Fetch resale tickets for this event from backend
    setResaleTickets([
      { id: 'r1', type: 'Regular', price: '₦4,500' },
      { id: 'r2', type: 'VIP', price: '₦13,000' },
    ]);
  }, []);

  // Share location handler
  const handleShareLocation = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission denied', 'Location permission is required to share your location.');
      return;
    }
    setSharing(true);
    let location = await Location.getCurrentPositionAsync({});
    // TODO: Send location.coords to your backend for sharing
    Alert.alert('Location shared!', `Lat: ${location.coords.latitude}, Lon: ${location.coords.longitude}`);
    setSharing(false);
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Image source={{ uri: event.imageUrl }} style={styles.image} />

      {/* Map View for Event Location */}
      <View style={{ height: 200, margin: 20, borderRadius: 12, overflow: 'hidden' }}>
        <MapView
          style={{ flex: 1 }}
          initialRegion={{
            latitude: eventCoords.latitude,
            longitude: eventCoords.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
        >
          <Marker coordinate={eventCoords} title={event.title} description={event.location} />
        </MapView>
      </View>

      {/* Share My Location Button */}
      <View style={{ marginHorizontal: 20, marginBottom: 10 }}>
        <Button
          title={sharing ? "Sharing..." : "Share My Location"}
          onPress={handleShareLocation}
          disabled={sharing}
        />
      </View>

      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text }]}>{event.title}</Text>
        
        <View style={styles.infoContainer}>
          <View style={styles.infoItem}>
            <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>{event.date}</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="time-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>{event.time}</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="location-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>{event.location}</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="person-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>{event.organizer}</Text>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>About</Text>
          <Text style={[styles.description, { color: theme.colors.text }]}>
            {event.description}
          </Text>
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Tickets</Text>
          
          {event.ticketTypes.map((ticket) => (
            <View
              key={ticket.id}
              style={[
                styles.ticketItem,
                { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
              ]}
            >
              <View>
                <Text style={[styles.ticketName, { color: theme.colors.text }]}>
                  {ticket.name}
                </Text>
                <Text style={[styles.ticketPrice, { color: theme.colors.primary }]}>
                  {ticket.price}
                </Text>
              </View>
              
              <TouchableOpacity
                style={[styles.selectButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => navigation.navigate('TicketPurchase', { eventId: event.id })}
              >
                <Text style={styles.selectButtonText}>Select</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Resale Marketplace</Text>
          {resaleTickets.length === 0 ? (
            <Text style={{ color: theme.colors.text }}>No resale tickets available.</Text>
          ) : (
            resaleTickets.map(ticket => (
              <View key={ticket.id} style={{ marginVertical: 8, padding: 12, backgroundColor: '#eee', borderRadius: 8 }}>
                <Text>{ticket.type} - {ticket.price}</Text>
                <Button title="Buy Resale Ticket" onPress={() => Alert.alert('Buy', `Buying ticket ${ticket.id}`)} />
              </View>
            ))
          )}
        </View>
      </View>
      
      <View style={styles.footer}>
        <Button
          title="Buy Tickets"
          onPress={() => navigation.navigate('TicketPurchase', { eventId: event.id })}
          style={{ flex: 1 }}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  image: {
    width: width,
    height: 250,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    lineHeight: 29,
    marginBottom: 20,
  },
  infoContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  infoText: {
    marginLeft: 5,
    fontSize: 16,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
  },
  ticketItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 10,
  },
  ticketName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  ticketPrice: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  selectButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  selectButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  footer: {
    padding: 20,
    paddingBottom: 30,
  },
});
