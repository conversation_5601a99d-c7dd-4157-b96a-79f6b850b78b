import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { DashboardStackParamList } from '../../navigation/HostNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../supabase';
import { useNavigation } from '@react-navigation/native';

type Props = NativeStackScreenProps<DashboardStackParamList, 'Dashboard'>;

// Mock data for now
const mockStats = {
  totalEvents: 5,
  upcomingEvents: 3,
  totalTicketsSold: 247,
  totalRevenue: '₦1,235,000',
};

const mockUpcomingEvents = [
  {
    id: '1',
    title: 'Summer Beach Party',
    date: 'June 15, 2023',
    ticketsSold: 120,
    totalCapacity: 200,
  },
  {
    id: '2',
    title: 'Jazz Night Live',
    date: 'June 20, 2023',
    ticketsSold: 85,
    totalCapacity: 150,
  },
  {
    id: '3',
    title: 'Tech Conference 2023',
    date: 'June 25, 2023',
    ticketsSold: 42,
    totalCapacity: 100,
  },
];

export default function DashboardScreen({ navigation }: Props) {
  const { theme, toggleTheme } = useTheme();
  const { user } = useAuth();
  const [stats, setStats] = useState(mockStats);
  const [upcomingEvents, setUpcomingEvents] = useState(mockUpcomingEvents);

  // Example: Toggle for analytics (could be a real setting in your backend)
  const [showAnalytics, setShowAnalytics] = useState(true);

  // In a real app, we would fetch data from Supabase here
  useEffect(() => {
    if (user) {
      // Fetch host stats and upcoming events
    }
  }, [user]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Dashboard</Text>
        <TouchableOpacity onPress={toggleTheme}>
          <Ionicons
            name={theme.isDark ? 'sunny-outline' : 'moon-outline'}
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
      </View>

      {/* Toggle for Analytics */}
      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
        <Ionicons name="bar-chart-outline" size={22} color={theme.colors.primary} style={{ marginRight: 8 }} />
        <Text style={{ color: theme.colors.text, fontSize: 16, marginRight: 8 }}>Show Analytics</Text>
        <TouchableOpacity
          style={{
            backgroundColor: showAnalytics ? theme.colors.primary : theme.colors.card,
            borderRadius: 12,
            paddingHorizontal: 14,
            paddingVertical: 6,
          }}
          onPress={() => setShowAnalytics(!showAnalytics)}
        >
          <Text style={{ color: showAnalytics ? '#fff' : theme.colors.text }}>
            {showAnalytics ? 'ON' : 'OFF'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Analytics Section */}
      {showAnalytics && (
        <View style={styles.statsContainer}>
          <View
            style={[
              styles.statCard,
              { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
            ]}
          >
            <Ionicons name="calendar-outline" size={24} color={theme.colors.primary} />
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              {stats.totalEvents}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.text + '80' }]}>
              Total Events
            </Text>
          </View>

          <View
            style={[
              styles.statCard,
              { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
            ]}
          >
            <Ionicons name="time-outline" size={24} color={theme.colors.primary} />
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              {stats.upcomingEvents}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.text + '80' }]}>
              Upcoming
            </Text>
          </View>

          <View
            style={[
              styles.statCard,
              { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
            ]}
          >
            <Ionicons name="ticket-outline" size={24} color={theme.colors.primary} />
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              {stats.totalTicketsSold}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.text + '80' }]}>
              Tickets Sold
            </Text>
          </View>

          <View
            style={[
              styles.statCard,
              { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
            ]}
          >
            <Ionicons name="cash-outline" size={24} color={theme.colors.primary} />
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              {stats.totalRevenue}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.text + '80' }]}>
              Revenue
            </Text>
          </View>
        </View>
      )}

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Upcoming Events
          </Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('EventsStack' as any)}
          >
            <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
              View All
            </Text>
          </TouchableOpacity>
        </View>

        {upcomingEvents.map((event) => (
          <TouchableOpacity
            key={event.id}
            style={[
              styles.eventCard,
              { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
            ]}
            onPress={() =>
              navigation.navigate('EventsStack' as any, {
                screen: 'EventDetails',
                params: { eventId: event.id },
              })
            }
          >
            <View>
              <Text style={[styles.eventTitle, { color: theme.colors.text }]}>
                {event.title}
              </Text>
              <Text style={[styles.eventDate, { color: theme.colors.text + '80' }]}>
                {event.date}
              </Text>
            </View>
            <View>
              <Text style={[styles.ticketsSold, { color: theme.colors.primary }]}>
                {event.ticketsSold}/{event.totalCapacity}
              </Text>
              <Text style={[styles.ticketsLabel, { color: theme.colors.text + '80' }]}>
                Tickets Sold
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Quick Actions
          </Text>
        </View>

        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[
              styles.actionButton,
              { backgroundColor: theme.colors.primary },
            ]}
            onPress={() =>
              navigation.navigate('EventsStack' as any, {
                screen: 'CreateEvent',
              })
            }
          >
            <Ionicons name="add-circle-outline" size={24} color="white" />
            <Text style={styles.actionButtonText}>Create Event</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.actionButton,
              { backgroundColor: theme.colors.secondary },
            ]}
            onPress={() => navigation.navigate('ScannerStack' as any)}
          >
            <Ionicons name="qr-code-outline" size={24} color="white" />
            <Text style={styles.actionButtonText}>Scan Tickets</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* In CreateEventScreen.tsx, add a toggle for "Allow Resale" when creating an event: */}
      <View style={{ flexDirection: 'row', alignItems: 'center', marginVertical: 16 }}>
        <Text style={{ fontSize: 16, marginRight: 12, color: theme.colors.text }}>
          Allow Ticket Resale
        </Text>
        <Switch
          value={false}
          onValueChange={() => {}}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.card}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    width: '48%',
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    marginBottom: 15,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 5,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 5,
  },
  section: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  viewAllText: {
    fontSize: 14,
  },
  eventCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 10,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  eventDate: {
    fontSize: 14,
  },
  ticketsSold: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  ticketsLabel: {
    fontSize: 12,
    textAlign: 'right',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '48%',
    padding: 15,
    borderRadius: 12,
  },
  actionButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 10,
  },
});
