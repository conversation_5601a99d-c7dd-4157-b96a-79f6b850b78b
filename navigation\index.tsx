
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAuth } from '../contexts/AuthContext';
import AuthNavigator from './AuthNavigator';
import GuestNavigator from './GuestNavigator';
import HostNavigator from './HostNavigator';
import RoleSelectionScreen from '../screens/auth/RoleSelectionScreen';
import LoadingScreen from '../screens/LoadingScreen';

export type RootStackParamList = {
  Auth: undefined;
  Guest: undefined;
  Host: undefined;
  RoleSelection: undefined;
  Loading: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function Navigation() {
  const { user, userRole, loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {!user ? (
        <Stack.Screen name="Auth" component={AuthNavigator} />
      ) : !userRole ? (
        <Stack.Screen name="RoleSelection" component={RoleSelectionScreen} />
      ) : userRole === 'guest' ? (
        <Stack.Screen name="Guest" component={GuestNavigator} />
      ) : (
        <Stack.Screen name="Host" component={HostNavigator} />
      )}
    </Stack.Navigator>
  );
}
