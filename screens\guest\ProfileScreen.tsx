import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Switch,
  Alert,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { ProfileStackParamList } from '../../navigation/GuestNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../supabase';
import { useIsFocused } from '@react-navigation/native';

type Props = NativeStackScreenProps<ProfileStackParamList, 'Profile'>;

// Mock user profile data
const mockProfile = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+234 ************',
  avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
  walletBalance: 15000, // initial mock balance
};

export default function ProfileScreen({ navigation }: Props) {
  const { theme, toggleTheme, isDark } = useTheme();
  const { user, signOut, userRole, setUserRole } = useAuth();
  const [profile, setProfile] = useState(mockProfile);
  const [loading, setLoading] = useState(false);
  const isFocused = useIsFocused();

  // Fetch wallet balance from backend (Supabase) when screen is focused or after resale
  useEffect(() => {
    async function fetchProfile() {
      if (user) {
        // Example: fetch wallet balance from Supabase
        const { data, error } = await supabase
          .from('profiles')
          .select('name, email, phone, avatar, wallet_balance')
          .eq('id', user.id)
          .single();
        if (!error && data) {
          setProfile({
            name: data.name,
            email: data.email,
            phone: data.phone,
            avatar: data.avatar,
            walletBalance: data.wallet_balance ?? 0,
          });
        }
      }
    }
    fetchProfile();
  }, [user, isFocused]);

  const handleSignOut = async () => {
    try {
      setLoading(true);
      await signOut();
    } catch (error) {
      Alert.alert('Error', 'Failed to sign out');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSwitchRole = async () => {
    try {
      setLoading(true);
      await setUserRole(userRole === 'guest' ? 'host' : 'guest');
      Alert.alert('Success', `Switched to ${userRole === 'guest' ? 'host' : 'guest'} mode`);
    } catch (error) {
      Alert.alert('Error', 'Failed to switch role');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Profile</Text>
      </View>

      {/* Wallet Balance Card */}
      <View style={[styles.walletCard, { backgroundColor: theme.colors.primary + '10', borderColor: theme.colors.primary }]}>
        <Ionicons name="wallet-outline" size={32} color={theme.colors.primary} style={{ marginRight: 12 }} />
        <View>
          <Text style={{ color: theme.colors.text + '80', fontSize: 14 }}>Wallet Balance</Text>
          <Text style={{ color: theme.colors.primary, fontSize: 22, fontWeight: 'bold' }}>
            ₦{profile.walletBalance?.toLocaleString() ?? '0'}
          </Text>
        </View>
      </View>

      <View style={styles.profileSection}>
        <Image source={{ uri: profile.avatar }} style={styles.avatar} />
        <Text style={[styles.name, { color: theme.colors.text }]}>{profile.name}</Text>
        <Text style={[styles.email, { color: theme.colors.text + '80' }]}>
          {profile.email}
        </Text>
        <TouchableOpacity
          style={[styles.editButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => navigation.navigate('EditProfile')}
        >
          <Text style={styles.editButtonText}>Edit Profile</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.settingsSection}>
        <View
          style={[
            styles.settingItem,
            { borderBottomColor: theme.colors.border, borderBottomWidth: 1 },
          ]}
        >
          <View style={styles.settingLeft}>
            <Ionicons name="moon-outline" size={24} color={theme.colors.text} />
            <Text style={[styles.settingText, { color: theme.colors.text }]}>
              Dark Mode
            </Text>
          </View>
          <Switch
            value={isDark}
            onValueChange={toggleTheme}
            trackColor={{ false: '#767577', true: theme.colors.primary + '80' }}
            thumbColor={isDark ? theme.colors.primary : '#f4f3f4'}
          />
        </View>

        <TouchableOpacity
          style={[
            styles.settingItem,
            { borderBottomColor: theme.colors.border, borderBottomWidth: 1 },
          ]}
          onPress={handleSwitchRole}
          disabled={loading}
        >
          <View style={styles.settingLeft}>
            <Ionicons name="swap-horizontal-outline" size={24} color={theme.colors.text} />
            <Text style={[styles.settingText, { color: theme.colors.text }]}>
              Switch to {userRole === 'guest' ? 'Host' : 'Guest'} Mode
            </Text>
          </View>
          <Ionicons
            name="chevron-forward"
            size={20}
            color={theme.colors.text + '80'}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.settingItem,
            { borderBottomColor: theme.colors.border, borderBottomWidth: 1 },
          ]}
        >
          <View style={styles.settingLeft}>
            <Ionicons name="help-circle-outline" size={24} color={theme.colors.text} />
            <Text style={[styles.settingText, { color: theme.colors.text }]}>
              Help & Support
            </Text>
          </View>
          <Ionicons
            name="chevron-forward"
            size={20}
            color={theme.colors.text + '80'}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.settingItem,
            { borderBottomColor: theme.colors.border, borderBottomWidth: 1 },
          ]}
        >
          <View style={styles.settingLeft}>
            <Ionicons name="document-text-outline" size={24} color={theme.colors.text} />
            <Text style={[styles.settingText, { color: theme.colors.text }]}>
              Terms & Privacy Policy
            </Text>
          </View>
          <Ionicons
            name="chevron-forward"
            size={20}
            color={theme.colors.text + '80'}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.settingItem}
          onPress={handleSignOut}
          disabled={loading}
        >
          <View style={styles.settingLeft}>
            <Ionicons name="log-out-outline" size={24} color={theme.colors.error} />
            <Text style={[styles.settingText, { color: theme.colors.error }]}>
              {loading ? 'Signing Out...' : 'Sign Out'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        <Text style={[styles.version, { color: theme.colors.text + '60' }]}>
          STEPPR v1.0.0
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  profileSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 15,
  },
  name: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  email: {
    fontSize: 14,
    marginBottom: 15,
  },
  editButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  editButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  settingsSection: {
    marginBottom: 30,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    fontSize: 16,
    marginLeft: 15,
  },
  footer: {
    alignItems: 'center',
    marginTop: 'auto',
  },
  version: {
    fontSize: 12,
  },
  walletCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 18,
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 20,
    marginHorizontal: 4,
  },
});
