import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export default function RoleSelectionScreen({ navigation }) {
  const { theme } = useTheme();

  const handleSelect = (role: 'guest' | 'host') => {
    // TODO: Save role to user profile (backend or context)
    // Then navigate to the correct navigator
    if (role === 'guest') {
      navigation.reset({ index: 0, routes: [{ name: 'GuestNavigator' }] });
    } else {
      navigation.reset({ index: 0, routes: [{ name: 'HostNavigator' }] });
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.title, { color: theme.colors.text }]}>I am a...</Text>
      <TouchableOpacity
        style={[styles.button, { backgroundColor: theme.colors.primary }]}
        onPress={() => handleSelect('guest')}
      >
        <Text style={styles.buttonText}>Guest (Partygoer)</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.button, { backgroundColor: theme.colors.primary }]}
        onPress={() => handleSelect('host')}
      >
        <Text style={styles.buttonText}>Host (Event Organizer)</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 24 },
  title: { fontSize: 28, fontWeight: 'bold', marginBottom: 32 },
  button: { width: '100%', padding: 18, borderRadius: 12, marginVertical: 12, alignItems: 'center' },
  buttonText: { color: '#fff', fontSize: 18, fontWeight: '600' },
});