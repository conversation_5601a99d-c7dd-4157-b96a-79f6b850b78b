# STEPPR Installation Guide

This guide will help you set up and run the STEPPR app on your local development environment.

## Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Expo CLI
- Supabase account

## Setup Steps

### 1. Clone the Repository

```bash
git clone <repository-url>
cd STEPPR
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
```

### 3. Set Up Supabase

1. Create a new Supabase project at https://supabase.com
2. Go to the SQL Editor in your Supabase dashboard
3. Run the SQL commands from the `supabase-schema.sql` file to set up the database schema
4. Get your Supabase URL and anon key from the API settings

### 4. Configure Environment Variables

1. Create a `.env` file in the root directory
2. Add your Supabase credentials:

```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

3. Update the `supabase.ts` file with your Supabase URL and anon key

### 5. Add Required Assets

1. Add the following images to the `assets` folder:
   - `guest-icon.png`
   - `host-icon.png`

### 6. Start the Development Server

```bash
npm start
# or
yarn start
```

This will start the Expo development server. You can run the app on:
- iOS simulator (requires macOS and Xcode)
- Android emulator (requires Android Studio)
- Physical device using the Expo Go app

## Testing the App

### Guest Flow
1. Sign up as a new user
2. Select "Guest" role
3. Explore events
4. Try purchasing a ticket
5. View your tickets in the STEPPS tab

### Host Flow
1. Sign up as a new user
2. Select "Host" role
3. Create a new event
4. View your events
5. Try the scanner functionality

## Troubleshooting

- If you encounter issues with dependencies, try running `npm install` or `yarn install` again
- Make sure your Supabase credentials are correct
- Check that the database schema is properly set up
- If you have issues with the Expo server, try running `expo start --clear`

## Next Steps

- Implement real payment processing
- Add push notifications
- Enhance the scanner functionality
- Implement offline mode for the scanner
- Add analytics for hosts
