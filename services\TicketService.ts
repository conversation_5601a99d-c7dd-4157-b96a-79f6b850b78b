import { supabase } from '../supabase';
import notificationService from './NotificationService';

export interface Ticket {
  id: string;
  ticket_type_id: string;
  user_id: string;
  event_id: string;
  qr_code: string;
  is_used: boolean;
  check_in_time: string | null;
  created_at: string;
  updated_at: string;
  // Additional fields from joins
  event_name?: string;
  event_date?: string;
  event_time?: string;
  ticket_type?: string;
  ticket_price?: number;
}

export interface TransferRequest {
  id: string;
  ticket_id: string;
  sender_id: string;
  recipient_email: string;
  recipient_id: string | null;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  created_at: string;
  expires_at: string;
}

class TicketService {
  /**
   * Get all tickets for the current user
   */
  async getUserTickets(): Promise<Ticket[]> {
    try {
      const { data: user } = await supabase.auth.getUser();
      
      if (!user || !user.user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('tickets')
        .select(`
          *,
          ticket_types(name, price),
          events(title, date, time)
        `)
        .eq('user_id', user.user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Format the data to match the Ticket interface
      return (data || []).map(ticket => ({
        ...ticket,
        event_name: ticket.events?.title,
        event_date: ticket.events?.date,
        event_time: ticket.events?.time,
        ticket_type: ticket.ticket_types?.name,
        ticket_price: ticket.ticket_types?.price,
      }));
    } catch (error) {
      console.error('Error fetching user tickets:', error);
      throw error;
    }
  }

  /**
   * Initiate a ticket transfer to another user
   */
  async initiateTicketTransfer(ticketId: string, recipientEmail: string): Promise<TransferRequest> {
    try {
      const { data: user } = await supabase.auth.getUser();
      
      if (!user || !user.user) {
        throw new Error('User not authenticated');
      }

      // Check if the user owns the ticket
      const { data: ticket, error: ticketError } = await supabase
        .from('tickets')
        .select('*')
        .eq('id', ticketId)
        .eq('user_id', user.user.id)
        .single();

      if (ticketError || !ticket) {
        throw new Error('Ticket not found or you do not have permission to transfer it');
      }

      if (ticket.is_used) {
        throw new Error('Cannot transfer a used ticket');
      }

      // Create an expiration date (24 hours from now)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // Create the transfer request
      const { data: transferRequest, error } = await supabase
        .from('transfer_requests')
        .insert({
          ticket_id: ticketId,
          sender_id: user.user.id,
          recipient_email: recipientEmail,
          status: 'pending',
          expires_at: expiresAt.toISOString(),
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Send email notification (in a real app)
      // For now, we'll just log it
      console.log(`Transfer request created for ticket ${ticketId} to ${recipientEmail}`);

      return transferRequest;
    } catch (error) {
      console.error('Error initiating ticket transfer:', error);
      throw error;
    }
  }

  /**
   * Accept a ticket transfer
   */
  async acceptTicketTransfer(transferRequestId: string): Promise<Ticket> {
    try {
      const { data: user } = await supabase.auth.getUser();
      
      if (!user || !user.user) {
        throw new Error('User not authenticated');
      }

      // Get the transfer request
      const { data: transferRequest, error: transferError } = await supabase
        .from('transfer_requests')
        .select('*')
        .eq('id', transferRequestId)
        .eq('recipient_email', user.user.email)
        .eq('status', 'pending')
        .single();

      if (transferError || !transferRequest) {
        throw new Error('Transfer request not found or already processed');
      }

      // Check if the transfer request has expired
      if (new Date(transferRequest.expires_at) < new Date()) {
        await supabase
          .from('transfer_requests')
          .update({ status: 'expired' })
          .eq('id', transferRequestId);
        
        throw new Error('Transfer request has expired');
      }

      // Start a transaction to update the ticket and transfer request
      const { data: ticket, error: ticketError } = await supabase
        .from('tickets')
        .update({ user_id: user.user.id })
        .eq('id', transferRequest.ticket_id)
        .select()
        .single();

      if (ticketError) {
        throw ticketError;
      }

      // Update the transfer request status
      await supabase
        .from('transfer_requests')
        .update({ 
          status: 'accepted',
          recipient_id: user.user.id
        })
        .eq('id', transferRequestId);

      // Send notification to the sender
      // In a real app, we would send a push notification
      // For now, we'll just log it
      console.log(`Transfer request ${transferRequestId} accepted by ${user.user.email}`);

      return ticket;
    } catch (error) {
      console.error('Error accepting ticket transfer:', error);
      throw error;
    }
  }

  /**
   * Reject a ticket transfer
   */
  async rejectTicketTransfer(transferRequestId: string): Promise<void> {
    try {
      const { data: user } = await supabase.auth.getUser();
      
      if (!user || !user.user) {
        throw new Error('User not authenticated');
      }

      // Update the transfer request status
      const { error } = await supabase
        .from('transfer_requests')
        .update({ status: 'rejected' })
        .eq('id', transferRequestId)
        .eq('recipient_email', user.user.email);

      if (error) {
        throw error;
      }

      // Send notification to the sender
      console.log(`Transfer request ${transferRequestId} rejected by ${user.user.email}`);
    } catch (error) {
      console.error('Error rejecting ticket transfer:', error);
      throw error;
    }
  }

  /**
   * Cancel a ticket transfer request
   */
  async cancelTicketTransfer(transferRequestId: string): Promise<void> {
    try {
      const { data: user } = await supabase.auth.getUser();
      
      if (!user || !user.user) {
        throw new Error('User not authenticated');
      }

      // Update the transfer request status
      const { error } = await supabase
        .from('transfer_requests')
        .update({ status: 'rejected' })
        .eq('id', transferRequestId)
        .eq('sender_id', user.user.id);

      if (error) {
        throw error;
      }

      console.log(`Transfer request ${transferRequestId} cancelled by sender`);
    } catch (error) {
      console.error('Error cancelling ticket transfer:', error);
      throw error;
    }
  }
}

const ticketService = new TicketService();

// Export individual functions for easier importing
export const transferTicket = ticketService.initiateTicketTransfer.bind(ticketService);
export const acceptTransfer = ticketService.acceptTicketTransfer.bind(ticketService);
export const rejectTransfer = ticketService.rejectTicketTransfer.bind(ticketService);
export const cancelTransfer = ticketService.cancelTicketTransfer.bind(ticketService);
export const getUserTickets = ticketService.getUserTickets.bind(ticketService);

export default ticketService;
