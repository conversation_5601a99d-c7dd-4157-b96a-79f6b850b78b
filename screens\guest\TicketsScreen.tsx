import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { TicketsStackParamList } from '../../navigation/GuestNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import TicketCard from '../../components/TicketCard';
import { supabase } from '../../supabase';
import { useAuth } from '../../contexts/AuthContext';

type Props = NativeStackScreenProps<TicketsStackParamList, 'Tickets'>;

// Mock data for now
const mockTickets = [
  {
    id: 'T1001',
    eventName: 'Summer Beach Party',
    date: 'June 15, 2023',
    time: '8:00 PM',
    ticketType: 'Regular' as const,
    qrValue: 'T1001-USER123',
    isUsed: false,
  },
  {
    id: 'T1002',
    eventName: 'Jazz Night Live',
    date: 'June 20, 2023',
    time: '7:30 PM',
    ticketType: 'VIP' as const,
    qrValue: 'T1002-USER123',
    isUsed: false,
  },
  {
    id: 'T1003',
    eventName: 'Tech Conference 2023',
    date: 'June 25, 2023',
    time: '9:00 AM',
    ticketType: 'Regular' as const,
    qrValue: 'T1003-USER123',
    isUsed: true,
  },
];

export default function TicketsScreen({ navigation }: Props) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [tickets, setTickets] = useState(mockTickets);
  const [activeTab, setActiveTab] = useState('upcoming');

  // In a real app, we would fetch tickets from Supabase here
  useEffect(() => {
    if (user) {
      // Filter tickets based on active tab
      if (activeTab === 'upcoming') {
        setTickets(mockTickets.filter(ticket => !ticket.isUsed));
      } else {
        setTickets(mockTickets.filter(ticket => ticket.isUsed));
      }
    }
  }, [user, activeTab]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>My STEPPS</Text>
      </View>

      <View style={[styles.tabContainer, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'upcoming' && [
              styles.activeTab,
              { backgroundColor: theme.colors.primary },
            ],
          ]}
          onPress={() => setActiveTab('upcoming')}
        >
          <Text
            style={[
              styles.tabText,
              {
                color:
                  activeTab === 'upcoming' ? 'white' : theme.colors.text,
              },
            ]}
          >
            Upcoming
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'past' && [
              styles.activeTab,
              { backgroundColor: theme.colors.primary },
            ],
          ]}
          onPress={() => setActiveTab('past')}
        >
          <Text
            style={[
              styles.tabText,
              {
                color: activeTab === 'past' ? 'white' : theme.colors.text,
              },
            ]}
          >
            Past
          </Text>
        </TouchableOpacity>
      </View>

      {tickets.length > 0 ? (
        <FlatList
          data={tickets}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TicketCard
              id={item.id}
              eventName={item.eventName}
              date={item.date}
              time={item.time}
              ticketType={item.ticketType}
              qrValue={item.qrValue}
              isUsed={item.isUsed}
              onPress={() => navigation.navigate('TicketDetail', { ticketId: item.id })}
            />
          )}
          contentContainerStyle={styles.ticketsList}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons
            name="ticket-outline"
            size={80}
            color={theme.colors.text + '40'}
          />
          <Text style={[styles.emptyText, { color: theme.colors.text }]}>
            {activeTab === 'upcoming'
              ? "You don't have any upcoming tickets"
              : "You don't have any past tickets"}
          </Text>
          <TouchableOpacity
            style={[styles.exploreButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => navigation.navigate('ExploreStack' as any)}
          >
            <Text style={styles.exploreButtonText}>Explore Events</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  tabContainer: {
    flexDirection: 'row',
    borderRadius: 25,
    marginBottom: 20,
    padding: 5,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 20,
  },
  activeTab: {
    borderRadius: 20,
  },
  tabText: {
    fontWeight: '500',
  },
  ticketsList: {
    paddingBottom: 20,
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 100,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 20,
    marginBottom: 30,
  },
  exploreButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  exploreButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});
