import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';

type RoleSelectionScreenProps = {
  navigation: NativeStackNavigationProp<any>;
};

export default function RoleSelectionScreen({ navigation }: RoleSelectionScreenProps) {
  const { setUserRole } = useAuth();
  const { theme } = useTheme();

  const handleRoleSelect = (role: 'guest' | 'host') => {
    setUserRole(role);
    navigation.replace('Home'); // Navigate to the home screen after selecting the role
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.title, { color: theme.colors.text }]}>Who are you here as?</Text>
      
      <View style={styles.optionsContainer}>
        <TouchableOpacity
          style={[
            styles.option,
            { backgroundColor: theme.colors.card, borderColor: theme.colors.border }
          ]}
          onPress={() => handleRoleSelect('guest')}
        >
          <View style={styles.iconContainer}>
            <Image
              source={require('../../assets/guest-icon.png')}
              style={styles.icon}
            />
          </View>
          <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Guest</Text>
          <Text style={[styles.optionDescription, { color: theme.colors.text + '80' }]}>
            I want to discover and attend events
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.option,
            { backgroundColor: theme.colors.card, borderColor: theme.colors.border }
          ]}
          onPress={() => handleRoleSelect('host')}
        >
          <View style={styles.iconContainer}>
            <Image
              source={require('../../assets/host-icon.png')}
              style={styles.icon}
            />
          </View>
          <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Host</Text>
          <Text style={[styles.optionDescription, { color: theme.colors.text + '80' }]}>
            I want to create and manage events
          </Text>
        </TouchableOpacity>
      </View>

      <Text style={[styles.note, { color: theme.colors.text + '80' }]}>
        You can change your role later in your profile settings
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 40,
  },
  optionsContainer: {
    marginBottom: 40,
  },
  option: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 20,
    marginBottom: 20,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  icon: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  optionDescription: {
    fontSize: 14,
  },
  note: {
    textAlign: 'center',
    fontSize: 12,
  },
});
