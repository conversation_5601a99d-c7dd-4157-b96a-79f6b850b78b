import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '../contexts/ThemeContext';
import TabIcon from '../components/TabIcon';

// Host Screens
import DashboardScreen from '../screens/host/DashboardScreen';
import EventsScreen from '../screens/host/EventsScreen';
import CreateEventScreen from '../screens/host/CreateEventScreen';
import EditEventScreen from '../screens/host/EditEventScreen';
import EventDetailsScreen from '../screens/host/EventDetailsScreen';
import AttendeesScreen from '../screens/host/AttendeesScreen';
import TicketsScreen from '../screens/guest/TicketsScreen'; // Reusing guest component for now
import TicketDetailScreen from '../screens/guest/TicketDetailScreen'; // Reusing guest component for now
import ScannerScreen from '../screens/host/ScannerScreen';
import HostProfileScreen from '../screens/host/HostProfileScreen';
import EditHostProfileScreen from '../screens/host/EditHostProfileScreen';

// Stack param lists
export type DashboardStackParamList = {
  Dashboard: undefined;
};

export type EventsStackParamList = {
  Events: undefined;
  CreateEvent: undefined;
  EditEvent: { eventId: string };
  EventDetails: { eventId: string };
  Attendees: { eventId: string };
};

export type TicketsStackParamList = {
  Tickets: undefined;
  TicketDetail: { ticketId: string };
};

export type ScannerStackParamList = {
  Scanner: undefined;
};

export type HostProfileStackParamList = {
  HostProfile: undefined;
  EditHostProfile: undefined;
};

// Tab param list
export type HostTabParamList = {
  DashboardStack: undefined;
  TicketsStack: undefined;
  ScannerStack: undefined;
  EventsStack: undefined;
  HostProfileStack: undefined;
};

// Create the stacks
const DashboardStack = createNativeStackNavigator<DashboardStackParamList>();
const EventsStack = createNativeStackNavigator<EventsStackParamList>();
const TicketsStack = createNativeStackNavigator<TicketsStackParamList>();
const ScannerStack = createNativeStackNavigator<ScannerStackParamList>();
const HostProfileStack = createNativeStackNavigator<HostProfileStackParamList>();
const Tab = createBottomTabNavigator<HostTabParamList>();

// Stack navigators
function DashboardStackNavigator() {
  return (
    <DashboardStack.Navigator>
      <DashboardStack.Screen name="Dashboard" component={DashboardScreen} options={{ headerShown: false }} />
    </DashboardStack.Navigator>
  );
}

function EventsStackNavigator() {
  return (
    <EventsStack.Navigator>
      <EventsStack.Screen name="Events" component={EventsScreen} options={{ headerShown: false }} />
      <EventsStack.Screen name="CreateEvent" component={CreateEventScreen} options={{ title: 'Create Event' }} />
      <EventsStack.Screen name="EditEvent" component={EditEventScreen} options={{ title: 'Edit Event' }} />
      <EventsStack.Screen name="EventDetails" component={EventDetailsScreen} options={{ title: 'Event Details' }} />
      <EventsStack.Screen name="Attendees" component={AttendeesScreen} options={{ title: 'Attendees' }} />
    </EventsStack.Navigator>
  );
}

function TicketsStackNavigator() {
  return (
    <TicketsStack.Navigator>
      <TicketsStack.Screen name="Tickets" component={TicketsScreen} options={{ headerShown: false }} />
      <TicketsStack.Screen name="TicketDetail" component={TicketDetailScreen} options={{ title: 'Ticket' }} />
    </TicketsStack.Navigator>
  );
}

function ScannerStackNavigator() {
  return (
    <ScannerStack.Navigator>
      <ScannerStack.Screen name="Scanner" component={ScannerScreen} options={{ headerShown: false }} />
    </ScannerStack.Navigator>
  );
}

function HostProfileStackNavigator() {
  return (
    <HostProfileStack.Navigator>
      <HostProfileStack.Screen name="HostProfile" component={HostProfileScreen} options={{ headerShown: false }} />
      <HostProfileStack.Screen name="EditHostProfile" component={EditHostProfileScreen} options={{ title: 'Edit Profile' }} />
    </HostProfileStack.Navigator>
  );
}

// Main tab navigator
export default function HostNavigator() {
  const { theme } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.text + '80',
        tabBarStyle: {
          backgroundColor: theme.colors.background,
          borderTopColor: 'transparent',
          height: 80,
          paddingTop: 10,
          paddingBottom: 20,
        },
        headerShown: false,
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      }}
    >
      <Tab.Screen
        name="DashboardStack"
        component={DashboardStackNavigator}
        options={{
          tabBarLabel: 'Explore',
          tabBarIcon: ({ color, size }) => (
            <TabIcon name="explore" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="TicketsStack"
        component={TicketsStackNavigator}
        options={{
          tabBarLabel: 'Tickets',
          tabBarIcon: ({ color, size }) => (
            <TabIcon name="tickets" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="ScannerStack"
        component={ScannerStackNavigator}
        options={{
          tabBarLabel: 'Scan',
          tabBarIcon: ({ color, size }) => (
            <TabIcon name="scan" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="EventsStack"
        component={EventsStackNavigator}
        options={{
          tabBarLabel: 'My Events',
          tabBarIcon: ({ color, size }) => (
            <TabIcon name="events" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="HostProfileStack"
        component={HostProfileStackNavigator}
        options={{
          tabBarLabel: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <TabIcon name="profile" color={color} size={size} />
          ),
        }}
      />
    </Tab.Navigator>
  );
}
