{"version": 3, "file": "LocationEventEmitter.js", "sourceRoot": "", "sources": ["../src/LocationEventEmitter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEjD,OAAO,YAAY,MAAM,gBAAgB,CAAC;AAE1C,MAAM,CAAC,MAAM,oBAAoB,GAAG,IAAI,YAAY,CAAC,YAAY,CAAC,CAAC", "sourcesContent": ["import { EventEmitter } from 'expo-modules-core';\n\nimport ExpoLocation from './ExpoLocation';\n\nexport const LocationEventEmitter = new EventEmitter(ExpoLocation);\n"]}